
import React from 'react';

const QuoteSection: React.FC = () => {
  return (
    <section className="bg-neutral-900 text-white py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
          <div className="md:w-2/3">
            <p className="font-serif text-2xl md:text-3xl lg:text-4xl leading-relaxed md:leading-loose mb-6">
              "People are like stained-glass windows. They sparkle and shine when the sun is out, but when the darkness sets in, their true beauty is revealed only if there is a light from within."
            </p>
            <div className="w-20 h-1 bg-gray-400 mb-4"></div>
            <p className="text-lg font-medium text-gray-300"><PERSON></p>
          </div>
          <div className="md:w-1/3 w-full">
            <img
              src="https://picsum.photos/seed/quote_author/400/400" // Placeholder for author image
              alt="<PERSON>"
              className="w-full h-auto max-h-80 object-cover rounded-xl shadow-lg grayscale"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default QuoteSection;
