import React, { useState, useEffect, useRef } from 'react';
import { CountryData, countryCodesData, searchCountries } from '../../data/countryCodes';
import { GlobeAltIcon, ChevronDownIcon, SearchIcon } from './Icon';

interface CountrySelectProps {
  value: string;
  onChange: (country: string, countryCode: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  error?: string;
}

const CountrySelect: React.FC<CountrySelectProps> = ({
  value,
  onChange,
  placeholder = "Select your country",
  required = false,
  disabled = false,
  className = "",
  error
}) => {
  const [selectedCountry, setSelectedCountry] = useState<CountryData | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<CountryData[]>(countryCodesData);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Initialize selected country
  useEffect(() => {
    if (value) {
      const country = countryCodesData.find(c => c.name === value);
      if (country) {
        setSelectedCountry(country);
      }
    }
  }, [value]);

  // Update filtered countries when search query changes
  useEffect(() => {
    if (searchQuery.trim()) {
      setFilteredCountries(searchCountries(searchQuery));
    } else {
      setFilteredCountries(countryCodesData);
    }
  }, [searchQuery]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setSearchQuery('');
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isDropdownOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isDropdownOpen]);

  const handleCountrySelect = (country: CountryData) => {
    setSelectedCountry(country);
    onChange(country.name, country.dialCode);
    setIsDropdownOpen(false);
    setSearchQuery('');
  };

  return (
    <div className={`relative ${className}`}>
      <label htmlFor="country" className="block text-sm font-semibold text-gray-700 mb-2">
        Country/Region {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          disabled={disabled}
          className={`
            w-full flex items-center text-left px-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm 
            border border-gray-200/50 hover:bg-white/70 focus:ring-2 focus:ring-brand-accent-teal/50 
            focus:border-brand-accent-teal transition-all duration-300 shadow-lg hover:shadow-xl
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-200' : ''}
          `}
        >
          <GlobeAltIcon className="text-gray-400 w-5 h-5 mr-3" />
          {selectedCountry ? (
            <div className="flex items-center space-x-3 flex-1">
              <span className="text-lg">{selectedCountry.flag}</span>
              <span className="text-sm font-medium text-gray-700">{selectedCountry.name}</span>
            </div>
          ) : (
            <span className="text-sm text-gray-500">{placeholder}</span>
          )}
          <ChevronDownIcon 
            size={16} 
            className={`text-gray-400 transition-transform duration-200 ml-auto ${
              isDropdownOpen ? 'rotate-180' : ''
            }`} 
          />
        </button>

        {/* Country Dropdown */}
        {isDropdownOpen && (
          <div className="absolute top-full left-0 mt-2 w-full bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 z-50 max-h-80 overflow-hidden">
            {/* Search Input */}
            <div className="p-3 border-b border-gray-200/50">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search countries..."
                  className="w-full pl-10 pr-4 py-2 rounded-xl bg-gray-50 border border-gray-200 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300 text-sm"
                />
              </div>
            </div>

            {/* Countries List */}
            <div className="max-h-64 overflow-y-auto custom-scrollbar">
              {filteredCountries.length > 0 ? (
                filteredCountries.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={() => handleCountrySelect(country)}
                    className={`
                      w-full flex items-center space-x-3 px-4 py-3 hover:bg-gray-100/50 
                      transition-colors text-left
                      ${selectedCountry?.code === country.code ? 'bg-brand-accent-teal/10' : ''}
                    `}
                  >
                    <span className="text-lg">{country.flag}</span>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {country.name}
                      </div>
                    </div>
                  </button>
                ))
              ) : (
                <div className="px-4 py-6 text-center text-gray-500 text-sm">
                  No countries found
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{error}</span>
        </p>
      )}
    </div>
  );
};

export default CountrySelect;
