import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Product } from '../types';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { ArrowLeftIcon, ArrowRightIcon, ShoppingCartIcon, ShareIcon, CheckIcon } from './common/Icon';
import styles from '../src/styles/animations.module.css';
import VariantSelector from './VariantSelector';
import { getProductIdFromSlug, isValidProductSlug } from '../utils/urlUtils';

// Extended product data with additional details for product pages
const getProductDetails = (productId: string): Product & {
  images: string[];
  detailedDescription: string;
  specifications: { [key: string]: string };
  benefits: string[];
  usage: string[];
} | null => {
  const baseProducts: { [key: string]: any } = {
    'sp1': {
      id: 'sp1',
      name: 'Abaya Shampoo',
      material: 'Gentle Formula for Delicate Fabrics',
      price: 24.99,
      original_price: 34.99,
      discount: '30% OFF',
      imageUrl: '/assets/products/abaya-shampoo.jpg',
      category: 'Fabric Care',
      features: ['Gentle', 'Fabric Safe'],
      images: [
        '/assets/products/abaya-shampoo.jpg',
        '/assets/products/abaya-shampoo.jpg',
        '/assets/products/abaya-shampoo.jpg'
      ],
      detailedDescription: 'Our premium Abaya Shampoo is specially formulated to provide gentle yet effective cleaning for delicate fabrics. This unique formula preserves the integrity and beauty of your precious garments while ensuring thorough cleansing.',
      specifications: {
        'Volume': '500ml',
        'pH Level': '6.5-7.0',
        'Fragrance': 'Light Floral',
        'Suitable For': 'Delicate Fabrics, Abayas, Silk',
        'Origin': 'UAE'
      },
      benefits: [
        'Gentle on delicate fabrics',
        'Preserves fabric integrity',
        'Light, pleasant fragrance',
        'Eco-friendly formula',
        'Concentrated formula for value'
      ],
      usage: [
        'Dilute 1 cap in 5 liters of water',
        'Soak garment for 5-10 minutes',
        'Gently hand wash or use delicate cycle',
        'Rinse thoroughly with clean water',
        'Air dry away from direct sunlight'
      ]
    },
    'sp2': {
      id: 'sp2',
      name: 'Premium Detergent Powder',
      material: 'Concentrated Cleaning Formula',
      price: 18.50,
      original_price: 25.99,
      discount: '25% OFF',
      imageUrl: '/assets/products/detergent-powder.jpg',
      category: 'Laundry Care',
      features: ['Concentrated', 'Stain Removal'],
      images: [
        '/assets/products/detergent-powder.jpg',
        '/assets/products/detergent-powder.jpg',
        '/assets/products/detergent-powder.jpg'
      ],
      detailedDescription: 'Experience the power of our Premium Detergent Powder, engineered with advanced cleaning technology to tackle the toughest stains while being gentle on your clothes and the environment.',
      specifications: {
        'Weight': '1kg',
        'Loads': 'Up to 40 loads',
        'Temperature': 'Cold to Hot Water',
        'Fabric Types': 'All Fabrics',
        'Phosphate Free': 'Yes'
      },
      benefits: [
        'Superior stain removal',
        'Concentrated formula',
        'Color protection technology',
        'Fresh, long-lasting scent',
        'Biodegradable ingredients'
      ],
      usage: [
        'Use 1-2 scoops for regular loads',
        'Pre-treat stains for best results',
        'Add to washing machine before clothes',
        'Suitable for all water temperatures',
        'Store in cool, dry place'
      ]
    },
    'sp3': {
      id: 'sp3',
      name: 'Dishwash Liquid',
      material: 'Grease-Cutting Formula',
      price: 12.75,
      original_price: 18.99,
      discount: '35% OFF',
      imageUrl: '/assets/products/dishwash-liquid.jpg',
      category: 'Kitchen Care',
      features: ['Grease-Cutting', 'Gentle on Hands'],
      images: [
        '/assets/products/dishwash-liquid.jpg',
        '/assets/products/dishwash-liquid.jpg',
        '/assets/products/dishwash-liquid.jpg'
      ],
      detailedDescription: 'Our advanced Dishwash Liquid combines powerful grease-cutting action with gentle care for your hands. Perfect for all your dishwashing needs, from everyday plates to the greasiest pans.',
      specifications: {
        'Volume': '750ml',
        'pH Level': '7.0-8.0',
        'Fragrance': 'Fresh Lemon',
        'Antibacterial': 'Yes',
        'Dermatologically Tested': 'Yes'
      },
      benefits: [
        'Cuts through grease instantly',
        'Gentle on hands',
        'Antibacterial protection',
        'Pleasant lemon fragrance',
        'Concentrated formula'
      ],
      usage: [
        'Apply small amount to sponge or directly to dishes',
        'Work into a rich lather',
        'Rinse thoroughly with water',
        'For tough grease, let sit for 2-3 minutes',
        'Safe for all dishware materials'
      ]
    },
    'sp4': {
      id: 'sp4',
      name: 'Professional Emulsifiers',
      material: 'Industrial Grade Cleaning Agent',
      price: 32.00,
      original_price: 45.99,
      discount: '30% OFF',
      imageUrl: '/assets/products/emulsifiers.jpg',
      category: 'Professional Care',
      features: ['Industrial Grade', 'Multi-Purpose'],
      images: [
        '/assets/products/emulsifiers.jpg',
        '/assets/products/emulsifiers.jpg',
        '/assets/products/emulsifiers.jpg'
      ],
      detailedDescription: 'Our Professional Emulsifiers are designed for industrial-grade cleaning applications. These powerful agents break down the toughest grease and grime, making them perfect for commercial and heavy-duty cleaning tasks.',
      specifications: {
        'Volume': '1L',
        'Concentration': 'High',
        'Application': 'Industrial/Commercial',
        'pH Level': '8.5-9.0',
        'Biodegradable': 'Yes'
      },
      benefits: [
        'Industrial-grade cleaning power',
        'Breaks down tough grease and grime',
        'Multi-purpose application',
        'Cost-effective concentrated formula',
        'Professional quality results'
      ],
      usage: [
        'Dilute according to cleaning task requirements',
        'Apply to surface and allow to penetrate',
        'Agitate with brush or cloth if needed',
        'Rinse thoroughly with clean water',
        'Use appropriate safety equipment'
      ]
    },
    'sp5': {
      id: 'sp5',
      name: 'Fabric Softener',
      material: 'Ultra-Soft Conditioning Formula',
      price: 16.25,
      original_price: 22.99,
      discount: '30% OFF',
      imageUrl: '/assets/products/fabric-softner.jpg',
      category: 'Laundry Care',
      features: ['Ultra-Soft', 'Fresh Scent'],
      images: [
        '/assets/products/fabric-softner.jpg',
        '/assets/products/fabric-softner.jpg',
        '/assets/products/fabric-softner.jpg'
      ],
      detailedDescription: 'Transform your laundry with our Ultra-Soft Fabric Softener. This premium conditioning formula leaves your clothes incredibly soft, reduces static, and infuses them with a long-lasting fresh scent.',
      specifications: {
        'Volume': '750ml',
        'Loads': 'Up to 30 loads',
        'Fragrance': 'Fresh Breeze',
        'Anti-Static': 'Yes',
        'Hypoallergenic': 'Yes'
      },
      benefits: [
        'Ultra-soft fabric conditioning',
        'Reduces static cling',
        'Long-lasting fresh fragrance',
        'Gentle on sensitive skin',
        'Works in all water temperatures'
      ],
      usage: [
        'Add to fabric softener dispenser',
        'Use 1 cap for regular loads',
        'For hand washing, add during final rinse',
        'Do not pour directly on clothes',
        'Store in cool, dry place'
      ]
    },
    'sp6': {
      id: 'sp6',
      name: 'Gentle Hand Wash',
      material: 'Moisturizing Hand Care Formula',
      price: 14.99,
      imageUrl: '/assets/products/handwash-1.jpg',
      category: 'Personal Care',
      features: ['Moisturizing', 'Antibacterial'],
      variants: [
        {
          id: 'green-apple',
          name: 'Green Apple',
          imageUrl: '/assets/products/handwash-1.jpg',
          color: { name: 'Green', hex: '#22C55E' }
        },
        {
          id: 'strawberry',
          name: 'Strawberry',
          imageUrl: '/assets/products/handwash-2.jpg',
          color: { name: 'Red', hex: '#EF4444' }
        },
        {
          id: 'lavender',
          name: 'Lavender',
          imageUrl: '/assets/products/handwash-3.jpg',
          color: { name: 'Purple', hex: '#A855F7' }
        }
      ],
      selectedVariant: 'green-apple',
      availableColorCount: 3,
      images: [
        '/assets/products/handwash-1.jpg',
        '/assets/products/handwash-2.jpg',
        '/assets/products/handwash-3.jpg'
      ],
      detailedDescription: 'Our Gentle Hand Wash combines effective cleansing with moisturizing care. Available in three delightful fragrances - Green Apple, Strawberry, and Lavender - this antibacterial formula keeps your hands clean, soft, and beautifully scented. Each variant offers a unique sensory experience while providing the same gentle yet effective cleansing power.',
      specifications: {
        'Volume': '300ml',
        'pH Level': '6.0-7.0',
        'Antibacterial': 'Yes',
        'Moisturizing Agents': 'Aloe Vera, Glycerin',
        'Available Flavors': 'Green Apple, Strawberry, Lavender',
        'Variants': '3 Different Fragrances'
      },
      benefits: [
        'Gentle antibacterial protection',
        'Moisturizes while cleansing',
        'Available in 3 beautiful fragrances',
        'Suitable for frequent use',
        'Dermatologically tested'
      ],
      usage: [
        'Apply small amount to wet hands',
        'Rub hands together for 20 seconds',
        'Rinse thoroughly with water',
        'Use as often as needed',
        'Suitable for all skin types'
      ]
    },
    'sp7': {
      id: 'sp7',
      name: 'Laundry Detergent',
      material: 'Advanced Cleaning Technology',
      price: 22.50,
      original_price: 32.99,
      discount: '35% OFF',
      imageUrl: '/assets/products/laundry-detergent.jpg',
      category: 'Laundry Care',
      features: ['Advanced Formula', 'Color Protection'],
      images: [
        '/assets/products/laundry-detergent.jpg',
        '/assets/products/laundry-detergent.jpg',
        '/assets/products/laundry-detergent.jpg'
      ],
      detailedDescription: 'Experience superior cleaning with our Advanced Laundry Detergent. This innovative formula combines powerful stain removal with color protection technology to keep your clothes looking vibrant and fresh.',
      specifications: {
        'Volume': '1L',
        'Loads': 'Up to 50 loads',
        'Formula': 'Concentrated Liquid',
        'Color Protection': 'Yes',
        'Enzyme Action': 'Multi-enzyme blend'
      },
      benefits: [
        'Advanced stain removal technology',
        'Protects fabric colors',
        'Works in all water temperatures',
        'Concentrated formula for value',
        'Fresh, clean scent'
      ],
      usage: [
        'Use 1-2 caps depending on load size',
        'Add to washing machine before clothes',
        'For tough stains, pre-treat directly',
        'Suitable for all fabric types',
        'Works in both top and front loaders'
      ]
    },
    'sp8': {
      id: 'sp8',
      name: 'Taze Fresh Cleaner',
      material: 'All-Purpose Cleaning Solution',
      price: 19.75,
      imageUrl: '/assets/products/taze.jpg',
      category: 'All-Purpose',
      features: ['Fresh Scent', 'Multi-Surface'],
      images: [
        '/assets/products/taze.jpg',
        '/assets/products/taze.jpg',
        '/assets/products/taze.jpg'
      ],
      detailedDescription: 'Taze Fresh Cleaner is your go-to solution for all-purpose cleaning. This versatile cleaner works on multiple surfaces, leaving behind a refreshing scent and spotless results every time.',
      specifications: {
        'Volume': '500ml',
        'Surface Types': 'Multi-surface',
        'Fragrance': 'Fresh Citrus',
        'Disinfectant': 'Yes',
        'Streak-Free': 'Yes'
      },
      benefits: [
        'Cleans multiple surface types',
        'Refreshing citrus fragrance',
        'Streak-free finish',
        'Disinfects while cleaning',
        'Easy-to-use spray bottle'
      ],
      usage: [
        'Spray directly onto surface',
        'Wipe with clean cloth or paper towel',
        'For tough stains, let sit for 30 seconds',
        'No rinsing required on most surfaces',
        'Test on inconspicuous area first'
      ]
    },
    'sp9': {
      id: 'sp9',
      name: 'Toilet Cleaner',
      material: 'Powerful Disinfectant Formula',
      price: 13.25,
      original_price: 19.99,
      discount: '35% OFF',
      imageUrl: '/assets/products/toilet-cleaner.jpg',
      category: 'Bathroom Care',
      features: ['Disinfectant', 'Deep Clean'],
      images: [
        '/assets/products/toilet-cleaner.jpg',
        '/assets/products/toilet-cleaner.jpg',
        '/assets/products/toilet-cleaner.jpg'
      ],
      detailedDescription: 'Our Powerful Toilet Cleaner provides deep cleaning and disinfection for your bathroom. This specialized formula eliminates germs, removes stains, and leaves your toilet sparkling clean with a fresh scent.',
      specifications: {
        'Volume': '750ml',
        'Disinfectant': '99.9% germ kill',
        'Stain Removal': 'Lime scale & rust',
        'Fragrance': 'Fresh Clean',
        'Toilet Bowl Safe': 'Yes'
      },
      benefits: [
        'Kills 99.9% of germs and bacteria',
        'Removes tough stains and lime scale',
        'Deep cleaning action',
        'Fresh, clean fragrance',
        'Safe for all toilet types'
      ],
      usage: [
        'Apply under toilet rim and bowl',
        'Allow to sit for 10 minutes',
        'Scrub with toilet brush',
        'Flush to rinse',
        'Use regularly for best results'
      ]
    }
  };

  return baseProducts[productId] || null;
};

const ProductDetailPage: React.FC = () => {
  const { productSlug } = useParams<{ productSlug: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addToCart } = useCart();
  const [product, setProduct] = useState<any>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [animationStage, setAnimationStage] = useState<'initial' | 'textFading' | 'iconSliding' | 'loading' | 'success' | 'complete'>('initial');

  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set());
  const [selectedVariantId, setSelectedVariantId] = useState<string>('');
  const sectionRef = useRef<HTMLDivElement>(null);
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  useEffect(() => {
    if (!productSlug) return;

    // Convert slug to product ID
    let productId: string;

    // Check if it's a valid slug
    if (isValidProductSlug(productSlug)) {
      productId = getProductIdFromSlug(productSlug);
    } else {
      // Fallback: treat as product ID for backward compatibility
      productId = productSlug;
    }

    const productData = getProductDetails(productId);

    // If product not found, redirect to store
    if (!productData) {
      navigate('/store');
      return;
    }

    setProduct(productData);

    // Update page title for SEO
    document.title = `${productData.name} - Le Prestine | Premium Cleaning Products`;

    // Initialize selected variant
    if (productData?.variants && productData.variants.length > 0) {
      setSelectedVariantId(productData.selectedVariant || productData.variants[0].id);
    }
  }, [productSlug, navigate]);

  // Cleanup: Reset page title when component unmounts
  useEffect(() => {
    return () => {
      document.title = 'Le Prestine | Premium Cleaning Products';
    };
  }, []);

  // Get current variant data
  const getCurrentVariant = () => {
    if (!product?.variants) return null;
    return product.variants.find((v: any) => v.id === selectedVariantId) || product.variants[0];
  };

  // Handle variant selection
  const handleVariantSelect = (variantId: string) => {
    setSelectedVariantId(variantId);
    setCurrentImageIndex(0); // Reset to first image when variant changes
  };

  // Get current images array (variant-specific or default)
  const getCurrentImages = () => {
    if (product?.variants && product.variants.length > 0) {
      // For products with variants, show only the selected variant image
      const currentVariant = getCurrentVariant();
      return currentVariant && currentVariant.imageUrl ? [currentVariant.imageUrl] : [product.imageUrl];
    }
    // For products without variants, use the original images array
    return product?.images || (product?.imageUrl ? [product.imageUrl] : []);
  };

  // Get current product image (variant-specific or default)
  const getCurrentProductImage = () => {
    const images = getCurrentImages();
    return images[currentImageIndex] || images[0];
  };

  // Navigation functions for image carousel
  const nextImage = () => {
    const images = getCurrentImages();
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    const images = getCurrentImages();
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  // Handle add to cart with animation sequence
  const handleAddToCart = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (isAddingToCart) return;
    setIsAddingToCart(true);

    try {
      // Start with text fade out
      setAnimationStage('textFading');
      
      // Wait for text to completely fade out
      await new Promise(resolve => setTimeout(resolve, 450));
      
      // Start cart slide animation
      setAnimationStage('iconSliding');
      
      // Wait for cart to slide and fade completely
      await new Promise(resolve => setTimeout(resolve, 800));
      setAnimationStage('loading');

      // Add to cart operation
      const result = await addToCart(product.id, 1, selectedVariantId);

      // Show success animation
      if (result.success) {
        setAnimationStage('success');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Set complete stage for fade in animation
        setAnimationStage('complete');
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Reset to initial state
        setAnimationStage('initial');
      } else {
        throw new Error(result.error || 'Failed to add item to cart');
      }
    } catch (error) {
      console.error('Failed to add to cart:', error);
      alert(error instanceof Error ? error.message : 'Failed to add item to cart');
      setAnimationStage('initial');
    } finally {
      setIsAddingToCart(false);
    }
  };



  // Intersection Observer for individual sections
  useEffect(() => {
    const observers: IntersectionObserver[] = [];
    
    Object.entries(sectionRefs.current).forEach(([key, element]) => {
      if (element) {
        const observer = new IntersectionObserver(
          ([entry]) => {
            if (entry.isIntersecting) {
              setVisibleSections(prev => new Set([...prev, key]));
            }
          },
          { threshold: 0.3 }
        );
        
        observer.observe(element);
        observers.push(observer);
      }
    });

    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, [product]);

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }



  return (
    <div ref={sectionRef} className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      <div className="relative z-10">
        {/* Breadcrumb Navigation */}
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 pt-4 sm:pt-6 lg:pt-8">
          <nav className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6 lg:mb-8">
            <button
              onClick={() => navigate('/')}
              className="hover:text-brand-accent-teal transition-colors duration-300 truncate"
            >
              Home
            </button>
            <span>/</span>
            <button
              onClick={() => navigate('/store')}
              className="hover:text-brand-accent-teal transition-colors duration-300 truncate"
            >
              Store
            </button>
            <span>/</span>
            <span className="text-brand-accent-teal font-medium truncate">{product.name}</span>
          </nav>

          {/* Go Back Navigation */}
          <div className="mb-4 sm:mb-6 lg:mb-8">
            <button
              onClick={() => navigate('/store')}
              className="group flex items-center space-x-2 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-700 hover:text-brand-accent-teal px-4 py-2.5 sm:px-6 sm:py-3 rounded-lg sm:rounded-xl border border-gray-200/50 hover:border-brand-accent-teal/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-medium text-sm sm:text-base"
            >
              <ArrowLeftIcon size={18} className="sm:w-5 sm:h-5 transform group-hover:-translate-x-1 transition-transform duration-300" />
              <span>Go Back</span>
            </button>
          </div>
        </div>

        {/* Main Product Section */}
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 pb-8 sm:pb-12 lg:pb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16 mb-8 sm:mb-12 lg:mb-16">
            {/* Product Images */}
            <div
              ref={(el) => { sectionRefs.current['images'] = el; }}
              className={`transition-all duration-700 ${
                visibleSections.has('images') ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'
              }`}
            >
              {/* Main Image */}
              <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-3 sm:p-4 md:p-6 shadow-2xl border border-white/50 mb-4 sm:mb-6 group overflow-hidden">
                {/* Glass Morphism Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

                <div className="relative aspect-square overflow-hidden rounded-xl sm:rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100">
                  <img
                    src={getCurrentProductImage() || '/assets/products/handwash-1.jpg'}
                    alt={`${product.name}${getCurrentVariant() ? ` - ${getCurrentVariant().name}` : ''}`}
                    className="w-full h-full object-cover transition-all duration-500 group-hover:scale-105"
                    onError={(e) => {
                      e.currentTarget.src = '/assets/products/handwash-1.jpg';
                    }}
                  />

                  {/* Navigation Arrows */}
                  {getCurrentImages().length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-2 sm:left-3 md:left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white/95 rounded-full p-2 sm:p-2.5 md:p-3 shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <ArrowLeftIcon size={16} className="sm:w-5 sm:h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-2 sm:right-3 md:right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white/95 rounded-full p-2 sm:p-2.5 md:p-3 shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <ArrowRightIcon size={16} className="sm:w-5 sm:h-5 text-gray-700" />
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Thumbnail Images */}
              {getCurrentImages().length > 1 && (
                <div className="flex space-x-2 sm:space-x-3 md:space-x-4 justify-center">
                  {getCurrentImages().map((image: string, index: number) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`relative w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-lg sm:rounded-xl overflow-hidden transition-all duration-300 ${
                        index === currentImageIndex
                          ? 'ring-2 ring-brand-accent-teal scale-110'
                          : 'hover:scale-105 opacity-70 hover:opacity-100'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${product.name} view ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information */}
            <div
              ref={(el) => { sectionRefs.current['info'] = el; }}
              className={`transition-all duration-700 ${
                visibleSections.has('info') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '200ms' }}
            >
              {/* Product Header */}
              <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50 mb-4 sm:mb-6">
                {/* Glass Morphism Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

                <div className="relative">
                  {/* Category Badge */}
                  <span className="inline-block bg-brand-accent-teal/10 text-brand-accent-teal px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium mb-3 sm:mb-4">
                    {product.category}
                  </span>

                  {/* Modern Discount Banner - if product has discount */}
                  {product.original_price && product.discount && (
                    <div className="mb-6 sm:mb-8">
                      <div className="relative overflow-hidden">
                        {/* Main discount card */}
                        <div className="bg-gradient-to-r from-brand-main-red via-red-500 to-red-600 text-white rounded-xl sm:rounded-2xl p-4 sm:p-5 md:p-6 shadow-2xl border border-red-400/20">
                          {/* Background pattern */}
                          <div className="absolute inset-0 opacity-10">
                            <div className="absolute inset-0" style={{
                              backgroundImage: `radial-gradient(circle at 20px 20px, rgba(255,255,255,0.3) 1px, transparent 0)`,
                              backgroundSize: '40px 40px'
                            }}></div>
                          </div>

                          {/* Content */}
                          <div className="relative flex items-center justify-between">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              {/* Discount icon */}
                              <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 2L13.09 8.26L20 9L15 14L16.18 21L10 17.27L3.82 21L5 14L0 9L6.91 8.26L10 2Z" clipRule="evenodd" />
                                </svg>
                              </div>

                              {/* Discount text */}
                              <div>
                                <div className="text-lg sm:text-xl md:text-2xl font-bold tracking-tight">
                                  {product.discount}
                                </div>
                                <div className="text-red-100 text-xs sm:text-sm font-medium">
                                  Special Offer
                                </div>
                              </div>
                            </div>

                            {/* Savings badge */}
                            <div className="bg-white/15 backdrop-blur-sm border border-white/20 rounded-lg sm:rounded-xl px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2">
                              <div className="text-white text-xs sm:text-sm font-semibold">
                                Save AED {(product.original_price - product.price).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Product Name */}
                  <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 leading-tight">
                    {product.name}
                    {getCurrentVariant() && (
                      <span className="block text-lg sm:text-xl md:text-2xl lg:text-3xl text-brand-accent-teal mt-1 sm:mt-2">
                        {getCurrentVariant().name} Flavor
                      </span>
                    )}
                  </h1>

                  {/* Material/Subtitle */}
                  <p className="text-sm sm:text-base md:text-lg text-gray-600 mb-4 sm:mb-5 md:mb-6 leading-relaxed">
                    {product.material}
                  </p>

                  {/* Modern Price Section */}
                  <div className="mb-6 sm:mb-8">
                    <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl sm:rounded-2xl p-4 sm:p-5 md:p-6 border border-gray-200/50 shadow-lg">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                        <div className="flex-1">
                          {/* Price comparison for discounted products */}
                          {product.original_price && product.discount ? (
                            <div className="space-y-2 sm:space-y-3">
                              {/* Original price with modern strikethrough */}
                              <div className="flex items-center space-x-2 sm:space-x-3">
                                <span className="text-sm sm:text-base md:text-lg text-gray-500 font-medium relative">
                                  <span className="relative">
                                    AED {product.original_price.toFixed(2)}
                                    <span className="absolute inset-0 flex items-center">
                                      <span className="w-full h-0.5 bg-gray-400 rounded-full"></span>
                                    </span>
                                  </span>
                                </span>
                                <span className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-md font-bold uppercase tracking-wide">
                                  {product.discount}
                                </span>
                              </div>

                              {/* Current price */}
                              <div className="flex items-baseline space-x-2">
                                <span className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">
                                  AED {product.price.toFixed(2)}
                                </span>
                                <span className="text-xs sm:text-sm text-gray-600 font-medium">
                                  Final Price
                                </span>
                              </div>

                              {/* Savings highlight */}
                              <div className="inline-flex items-center bg-green-50 border border-green-200 text-green-700 px-2 py-1 sm:px-3 sm:py-1.5 rounded-lg">
                                <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span className="text-xs sm:text-sm font-semibold">
                                  You save AED {(product.original_price - product.price).toFixed(2)}
                                </span>
                              </div>
                            </div>
                          ) : (
                            /* Regular price for non-discounted products */
                            <div className="space-y-1 sm:space-y-2">
                              <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">
                                AED {product.price.toFixed(2)}
                              </div>
                              <div className="text-xs sm:text-sm text-gray-600 font-medium">
                                Premium Quality
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex space-x-2 sm:space-x-3 sm:ml-6">
                          <button className="group p-2 sm:p-2.5 md:p-3 bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 rounded-lg sm:rounded-xl transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md">
                            <ShareIcon size={18} className="sm:w-5 sm:h-5 text-gray-600 group-hover:text-gray-700" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Variant Selector */}
                  {product.variants && product.variants.length > 0 && (
                    <div className="mb-6 sm:mb-8">
                      <VariantSelector
                        variants={product.variants}
                        selectedVariantId={selectedVariantId}
                        onVariantSelect={handleVariantSelect}
                      />
                    </div>
                  )}


                  {/* Action Buttons */}
                  <div className="flex flex-col">
                    {/* Add to Cart Button with Animation */}
                    <button
                      onClick={handleAddToCart}
                      disabled={isAddingToCart}
                      className={`w-full relative py-3 px-6 sm:py-4 sm:px-8 rounded-xl sm:rounded-2xl font-semibold text-base sm:text-lg transition-all duration-300 flex items-center justify-center space-x-2 sm:space-x-3 group disabled:cursor-not-allowed min-h-[44px] sm:min-h-[48px] ${
                        animationStage === 'success'
                          ? styles.successBackground
                          : 'bg-gradient-to-r from-brand-accent-teal to-brand-main-red'
                      } text-white ${styles.cartButton}`}
                    >
                      {/* Cart Icon */}
                      {animationStage !== 'loading' && animationStage !== 'success' && (
                        <ShoppingCartIcon
                          size={20}
                          className={`sm:w-6 sm:h-6 ${
                            animationStage === 'iconSliding'
                              ? styles.cartIconSliding
                              : `${styles.cartIcon} ${animationStage === 'complete' ? styles.fadeIn : ''}`
                          }`}
                        />
                      )}

                      {/* Text */}
                      <span className={`${styles.cartText} ${
                        animationStage === 'textFading'
                          ? styles.cartTextFading
                          : animationStage === 'complete'
                            ? styles.fadeIn
                            : animationStage === 'initial'
                              ? 'opacity-100'
                              : 'opacity-0'
                      }`}>
                        Add to Cart
                      </span>

                      {/* Loading Spinner */}
                      {animationStage === 'loading' && (
                        <div className={`absolute ${styles.loadingSpinner}`} />
                      )}

                      {/* Success Checkmark */}
                      {animationStage === 'success' && (
                        <div className={`absolute ${styles.checkmark} ${styles.checkmarkAppearing}`}>
                          <CheckIcon size={20} className="sm:w-6 sm:h-6" />
                        </div>
                      )}
                    </button>
                  </div>

                </div>
              </div>

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

                  <div className="relative">
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Key Features</h3>
                    <div className="flex flex-wrap gap-2 sm:gap-3">
                      {product.features.map((feature: string, index: number) => (
                        <span
                          key={index}
                          className="bg-brand-accent-teal/10 text-brand-accent-teal px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Detailed Information Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-12 lg:mb-16">
            {/* Description */}
            <div
              ref={(el) => { sectionRefs.current['description'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('description') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '300ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-4 sm:mb-5 md:mb-6">Product Description</h3>
                <p className="text-gray-700 leading-relaxed text-sm sm:text-base md:text-lg">
                  {product.detailedDescription}
                </p>
              </div>
            </div>

            {/* Specifications */}
            <div
              ref={(el) => { sectionRefs.current['specifications'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('specifications') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '400ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-4 sm:mb-5 md:mb-6">Specifications</h3>
                <div className="space-y-3 sm:space-y-4">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-gray-200/50 last:border-b-0 gap-1 sm:gap-0">
                      <span className="font-medium text-gray-700 text-sm sm:text-base">{key}:</span>
                      <span className="text-gray-900 font-semibold text-sm sm:text-base">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Benefits and Usage */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-12 lg:mb-16">
            {/* Benefits */}
            <div
              ref={(el) => { sectionRefs.current['benefits'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('benefits') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '500ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-4 sm:mb-5 md:mb-6">Benefits</h3>
                <ul className="space-y-2 sm:space-y-3">
                  {product.benefits.map((benefit: string, index: number) => (
                    <li key={index} className="flex items-start space-x-2 sm:space-x-3">
                      <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-brand-accent-teal rounded-full mt-2 sm:mt-3 flex-shrink-0"></div>
                      <span className="text-gray-700 leading-relaxed text-sm sm:text-base">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Usage Instructions */}
            <div
              ref={(el) => { sectionRefs.current['usage'] = el; }}
              className={`relative bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl border border-white/50 transition-all duration-700 ${
                visibleSections.has('usage') ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
              }`}
              style={{ transitionDelay: '600ms' }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl"></div>

              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-4 sm:mb-5 md:mb-6">How to Use</h3>
                <ol className="space-y-2 sm:space-y-3">
                  {product.usage.map((step: string, index: number) => (
                    <li key={index} className="flex items-start space-x-3 sm:space-x-4">
                      <div className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 bg-brand-accent-teal text-white rounded-full flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-gray-700 leading-relaxed pt-0.5 sm:pt-1 text-sm sm:text-base">{step}</span>
                    </li>
                  ))}
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
