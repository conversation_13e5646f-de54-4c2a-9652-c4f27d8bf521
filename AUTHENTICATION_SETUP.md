# Le Prestine Authentication System Setup Guide

## Overview
This guide provides complete setup instructions for the Le Prestine authentication system using Supabase as the backend.

## Features Implemented
- ✅ User registration (signup) with comprehensive data collection
- ✅ User login with email/password
- ✅ User profile management
- ✅ Protected routes
- ✅ Authentication-aware navigation
- ✅ Modern, professional UI with glass morphism effects
- ✅ Responsive design
- ✅ Form validation and error handling

## Prerequisites
- Node.js (v16 or higher)
- Supabase account
- Git

## 1. Supabase Database Setup

### Step 1: Execute Database Schema
Go to your Supabase project dashboard → SQL Editor and execute the following SQL:

```sql
-- Le Prestine Authentication Database Schema
-- Execute this SQL in your Supabase SQL Editor

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone_number TEXT,
    country_code TEXT,
    country TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER handle_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for user_profiles table
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Users can delete their own profile
CREATE POLICY "Users can delete own profile" ON public.user_profiles
    FOR DELETE USING (auth.uid() = id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_profiles_email_idx ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS user_profiles_country_idx ON public.user_profiles(country);
CREATE INDEX IF NOT EXISTS user_profiles_created_at_idx ON public.user_profiles(created_at);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO anon;
```

### Step 2: Configure Authentication Settings
1. Go to Authentication → Settings in your Supabase dashboard
2. Enable email confirmations if desired (recommended for production)
3. Configure email templates if needed

## 2. Environment Configuration

Your `.env.local` file has been updated with the Supabase credentials:

```env
GEMINI_API_KEY=PLACEHOLDER_API_KEY

# Supabase Configuration
VITE_SUPABASE_URL=https://whhmvygcncbbkochckyn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoaG12eWdjbmNiYmtvY2hja3luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5NTk2NTEsImV4cCI6MjA2NDUzNTY1MX0.R7U7_Hhu1nhFGy9cKl57Fj3OHpTmPOYYpNkS3TFepSU
```

## 3. Dependencies Installed

The following package has been added to your project:
- `@supabase/supabase-js` - Supabase JavaScript client

## 4. New Routes Available

- `/login` - User login page
- `/signup` - User registration page
- `/profile` - User profile page (protected route)

## 5. Components Created

### Authentication Components
- `components/auth/LoginPage.tsx` - Login form
- `components/auth/SignupPage.tsx` - Registration form
- `components/auth/ProfilePage.tsx` - User profile management
- `components/auth/ProtectedRoute.tsx` - Route protection wrapper

### Context & Configuration
- `contexts/AuthContext.tsx` - Authentication state management
- `lib/supabase.ts` - Supabase client configuration

### Database Schema
- `database/schema.sql` - Complete database schema

## 6. User Data Collection

The signup form collects the following information:
- ✅ Email address (required, primary identifier)
- ✅ Full name (first name and last name)
- ✅ Phone number with country code selector
- ✅ Country/region selection
- ✅ Password with confirmation

## 7. Features Overview

### Authentication Flow
1. **Signup**: Users register with comprehensive information
2. **Email Verification**: Optional email confirmation (configurable)
3. **Login**: Email/password authentication
4. **Profile Management**: Users can update their information
5. **Logout**: Secure session termination

### Security Features
- Row Level Security (RLS) enabled
- User data isolation
- Secure password handling
- Protected routes
- Session management

### UI/UX Features
- Modern glass morphism design
- Smooth animations and transitions
- Responsive design
- Form validation
- Error handling
- Loading states
- Success feedback

## 8. Testing the Implementation

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Test the authentication flow**:
   - Visit `http://localhost:5174/signup` to create an account
   - Visit `http://localhost:5174/login` to sign in
   - Visit `http://localhost:5174/profile` to view/edit profile
   - Click the user icon in the header for quick access

3. **Test protected routes**:
   - Try accessing `/profile` without being logged in
   - Should redirect to login page

## 9. Customization Options

### Styling
- All components use your existing design system
- Glass morphism effects consistent with your brand
- Colors match your brand palette (teal and red)

### Validation
- Form validation can be customized in each component
- Error messages can be modified
- Additional fields can be added to the signup form

### Email Templates
- Customize email templates in Supabase dashboard
- Add your branding and styling

## 10. Production Considerations

1. **Email Verification**: Enable email confirmation for production
2. **Rate Limiting**: Configure rate limiting in Supabase
3. **Password Policies**: Set strong password requirements
4. **CORS**: Configure CORS settings for your domain
5. **Environment Variables**: Secure your environment variables

## 11. Troubleshooting

### Common Issues
1. **Database Connection**: Ensure Supabase credentials are correct
2. **RLS Policies**: Verify Row Level Security policies are active
3. **Email Delivery**: Check Supabase email settings
4. **CORS Errors**: Verify domain configuration

### Support
- Check Supabase documentation: https://supabase.com/docs
- Review authentication logs in Supabase dashboard
- Test database queries in SQL Editor

## 12. Next Steps

1. **Test thoroughly** in development
2. **Configure email templates** for your brand
3. **Set up production environment** variables
4. **Deploy to production** when ready
5. **Monitor user registrations** in Supabase dashboard

The authentication system is now fully integrated and ready for use!
