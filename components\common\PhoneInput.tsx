import React, { useState, useEffect, useRef } from 'react';
import { CountryData, countryCodesData, searchCountries } from '../../data/countryCodes';
import { geolocationService } from '../../services/geolocationService';
import { PhoneNumberValidator } from '../../utils/phoneValidation';
import { PhoneIcon, ChevronDownIcon, SearchIcon } from './Icon';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  countryCode: string;
  onCountryChange: (countryCode: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  error?: string;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  countryCode,
  onCountryChange,
  placeholder = "Phone number",
  required = false,
  disabled = false,
  className = "",
  error
}) => {
  const [selectedCountry, setSelectedCountry] = useState<CountryData | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<CountryData[]>(countryCodesData);
  const [validationError, setValidationError] = useState<string>('');
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [hasAutoDetected, setHasAutoDetected] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Initialize selected country and auto-detect location
  useEffect(() => {
    const initializeCountry = async () => {
      // First, try to find country by provided country code
      if (countryCode) {
        const country = countryCodesData.find(c => c.dialCode === countryCode);
        if (country) {
          setSelectedCountry(country);
          return;
        }
      }

      // If no country code provided or not found, auto-detect from IP
      if (!hasAutoDetected) {
        setIsDetectingLocation(true);
        try {
          const locationResult = await geolocationService.getUserLocation();
          if (locationResult.success && locationResult.data) {
            const detectedCountry = countryCodesData.find(
              c => c.code === locationResult.data!.country_code2
            );
            
            if (detectedCountry) {
              setSelectedCountry(detectedCountry);
              onCountryChange(detectedCountry.dialCode);
              setHasAutoDetected(true);
            }
          }
        } catch (error) {
          console.warn('Failed to auto-detect location:', error);
        } finally {
          setIsDetectingLocation(false);
        }
      }

      // Fallback to US if nothing else works
      if (!selectedCountry && !isDetectingLocation) {
        const fallbackCountry = countryCodesData.find(c => c.code === 'US');
        if (fallbackCountry) {
          setSelectedCountry(fallbackCountry);
          onCountryChange(fallbackCountry.dialCode);
        }
      }
    };

    initializeCountry();
  }, [countryCode, onCountryChange, hasAutoDetected, selectedCountry, isDetectingLocation]);

  // Update filtered countries when search query changes
  useEffect(() => {
    if (searchQuery.trim()) {
      setFilteredCountries(searchCountries(searchQuery));
    } else {
      setFilteredCountries(countryCodesData);
    }
  }, [searchQuery]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setSearchQuery('');
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isDropdownOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isDropdownOpen]);

  // Validate phone number on change
  useEffect(() => {
    if (value && selectedCountry) {
      const validation = PhoneNumberValidator.validatePhoneNumber(value, selectedCountry);
      if (!validation.isValid && validation.error) {
        setValidationError(validation.error);
      } else {
        setValidationError('');
      }
    } else {
      setValidationError('');
    }
  }, [value, selectedCountry]);

  const handleCountrySelect = (country: CountryData) => {
    setSelectedCountry(country);
    onCountryChange(country.dialCode);
    setIsDropdownOpen(false);
    setSearchQuery('');
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Allow only digits, spaces, dashes, parentheses, and plus sign
    const cleanValue = inputValue.replace(/[^\d\s\-\(\)\+]/g, '');
    
    // Auto-format as user types (basic formatting)
    if (selectedCountry && cleanValue) {
      const validation = PhoneNumberValidator.validatePhoneNumber(cleanValue, selectedCountry);
      if (validation.isValid && validation.formattedNumber) {
        onChange(validation.formattedNumber);
      } else {
        onChange(cleanValue);
      }
    } else {
      onChange(cleanValue);
    }
  };

  const getExampleNumber = () => {
    if (selectedCountry) {
      return PhoneNumberValidator.getExampleNumber(selectedCountry);
    }
    return placeholder;
  };

  const displayError = error || validationError;

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-semibold text-gray-700 mb-2">
        Phone Number {required && <span className="text-red-500">*</span>}
        {isDetectingLocation && (
          <span className="ml-2 text-xs text-brand-accent-teal animate-pulse">
            Detecting location...
          </span>
        )}
      </label>
      
      <div className="flex space-x-3">
        {/* Country Code Display */}
        <div className="relative" ref={dropdownRef}>
          <div className={`
              flex items-center space-x-2 px-3 py-4 rounded-2xl bg-white/60 backdrop-blur-sm 
              border border-gray-200/50 transition-all duration-300 shadow-lg
              ${disabled ? 'opacity-50' : ''}
              ${displayError ? 'border-red-300' : ''}
            `}
          >
            {selectedCountry ? (
              <>
                <span className="text-lg">{selectedCountry.flag}</span>
                <span className="text-sm font-medium text-gray-700">
                  {selectedCountry.dialCode}
                </span>
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-gray-300 rounded animate-pulse"></div>
                <span className="text-sm text-gray-500">+1</span>
              </div>
            )}
          </div>
        </div>

        {/* Phone Number Input */}
        <div className="flex-1 group">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <PhoneIcon className={`text-gray-400 group-focus-within:text-brand-accent-teal transition-colors ${displayError ? 'text-red-400' : ''}`} size={20} />
            </div>
            <input
              type="tel"
              value={value}
              onChange={handlePhoneChange}
              placeholder={getExampleNumber()}
              required={required}
              disabled={disabled}
              className={`
                w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 
                focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 
                transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                ${displayError ? 'border-red-300 focus:border-red-500 focus:ring-red-200' : ''}
              `}
            />
            <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none ${displayError ? 'from-red-500/5 to-red-500/5' : ''}`}></div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {displayError && (
        <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{displayError}</span>
        </p>
      )}

      {/* Helper Text */}
      {!displayError && selectedCountry && (
        <p className="mt-2 text-xs text-gray-500">
          Example: {PhoneNumberValidator.getExampleNumber(selectedCountry)}
        </p>
      )}
    </div>
  );
};

export default PhoneInput;
