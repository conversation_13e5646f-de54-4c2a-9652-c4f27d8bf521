-- Le Prestine Shopping Cart Database Schema
-- Execute this SQL in your Supabase SQL Editor
-- This script only creates the cart-related tables and functions

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    discount TEXT,
    image_url TEXT NOT NULL,
    description TEXT,
    material TEXT,
    features TEXT[], -- Array of features
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create cart_items table
CREATE TABLE IF NOT EXISTS public.cart_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    product_id TEXT REFERENCES public.products(id) ON DELETE CASCADE NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(user_id, product_id) -- Prevent duplicate items for same user
);

-- Create updated_at trigger for products (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'handle_products_updated_at' 
        AND tgrelid = 'public.products'::regclass
    ) THEN
        CREATE TRIGGER handle_products_updated_at
            BEFORE UPDATE ON public.products
            FOR EACH ROW
            EXECUTE FUNCTION public.handle_updated_at();
    END IF;
END $$;

-- Create updated_at trigger for cart_items (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'handle_cart_items_updated_at' 
        AND tgrelid = 'public.cart_items'::regclass
    ) THEN
        CREATE TRIGGER handle_cart_items_updated_at
            BEFORE UPDATE ON public.cart_items
            FOR EACH ROW
            EXECUTE FUNCTION public.handle_updated_at();
    END IF;
END $$;

-- Enable Row Level Security for products and cart_items
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;

-- Products policies (public read access)
DROP POLICY IF EXISTS "Anyone can view products" ON public.products;
CREATE POLICY "Anyone can view products" ON public.products
    FOR SELECT USING (true);

-- Cart items policies (user-specific access)
DROP POLICY IF EXISTS "Users can view own cart items" ON public.cart_items;
CREATE POLICY "Users can view own cart items" ON public.cart_items
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own cart items" ON public.cart_items;
CREATE POLICY "Users can insert own cart items" ON public.cart_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own cart items" ON public.cart_items;
CREATE POLICY "Users can update own cart items" ON public.cart_items
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own cart items" ON public.cart_items;
CREATE POLICY "Users can delete own cart items" ON public.cart_items
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS products_category_idx ON public.products(category);
CREATE INDEX IF NOT EXISTS products_price_idx ON public.products(price);
CREATE INDEX IF NOT EXISTS cart_items_user_id_idx ON public.cart_items(user_id);
CREATE INDEX IF NOT EXISTS cart_items_product_id_idx ON public.cart_items(product_id);
CREATE INDEX IF NOT EXISTS cart_items_user_product_idx ON public.cart_items(user_id, product_id);

-- Create function to handle cart item upsert
CREATE OR REPLACE FUNCTION public.upsert_cart_item(
    p_user_id UUID,
    p_product_id TEXT,
    p_quantity INTEGER
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.cart_items (user_id, product_id, quantity)
    VALUES (p_user_id, p_product_id, p_quantity)
    ON CONFLICT (user_id, product_id)
    DO UPDATE SET 
        quantity = cart_items.quantity + p_quantity,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.upsert_cart_item(UUID, TEXT, INTEGER) TO authenticated;

-- Insert sample products data with discount information
INSERT INTO public.products (id, name, category, price, original_price, discount, image_url, description, material, features) VALUES
('sp1', 'Abaya Shampoo', 'Laundry & Fabric Care', 24.99, 34.99, '30% OFF', '/assets/products/abaya-shampoo.jpg', 'Gentle formula specially designed for delicate fabrics like abayas', 'Gentle Formula for Delicate Fabrics', ARRAY['Gentle', 'Fabric Safe']),
('sp2', 'Premium Detergent Powder', 'Laundry & Fabric Care', 18.50, 25.99, '25% OFF', '/assets/products/detergent-powder.jpg', 'Concentrated cleaning formula for effective stain removal', 'Concentrated Cleaning Formula', ARRAY['Concentrated', 'Stain Removal']),
('sp3', 'Fabric Softener', 'Laundry & Fabric Care', 15.75, NULL, NULL, '/assets/products/fabric-softener.jpg', 'Premium fabric softener for ultimate comfort', 'Premium Softening Formula', ARRAY['Softening', 'Fresh Scent']),
('sp4', 'Stain Remover Spray', 'Laundry & Fabric Care', 12.99, 18.99, '35% OFF', '/assets/products/stain-remover.jpg', 'Powerful stain removal for tough stains', 'Advanced Stain Fighting Technology', ARRAY['Powerful', 'Quick Action']),
('sp5', 'Delicate Wash', 'Laundry & Fabric Care', 21.25, 29.99, '30% OFF', '/assets/products/delicate-wash.jpg', 'Gentle care for your most precious garments', 'Ultra-Gentle Cleaning Formula', ARRAY['Gentle', 'Color Protection']),
('sp6', 'Multi-Surface Cleaner', 'Home & Surface Care', 16.99, NULL, NULL, '/assets/products/multi-surface-cleaner.jpg', 'Versatile cleaner for all surfaces', 'All-Purpose Cleaning Solution', ARRAY['Versatile', 'Streak-Free']),
('sp7', 'Laundry Detergent', 'Laundry & Fabric Care', 22.50, 32.99, '35% OFF', '/assets/products/laundry-detergent.jpg', 'Advanced cleaning technology with color protection', 'Advanced Cleaning Technology', ARRAY['Advanced Formula', 'Color Protection']),
('sp8', 'Glass Cleaner', 'Home & Surface Care', 13.75, NULL, NULL, '/assets/products/glass-cleaner.jpg', 'Crystal clear results for all glass surfaces', 'Streak-Free Glass Formula', ARRAY['Streak-Free', 'Crystal Clear']),
('sp9', 'Floor Cleaner', 'Home & Surface Care', 19.99, 27.99, '30% OFF', '/assets/products/floor-cleaner.jpg', 'Deep cleaning power for all floor types', 'Multi-Floor Cleaning Formula', ARRAY['Deep Clean', 'Fresh Scent']),
('sp10', 'Bathroom Cleaner', 'Home & Surface Care', 17.50, 24.99, '30% OFF', '/assets/products/bathroom-cleaner.jpg', 'Powerful bathroom cleaning solution', 'Anti-Bacterial Formula', ARRAY['Anti-Bacterial', 'Lime Scale Removal'])
ON CONFLICT (id) DO UPDATE SET
  original_price = EXCLUDED.original_price,
  discount = EXCLUDED.discount;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.products TO anon, authenticated;
GRANT ALL ON public.cart_items TO authenticated;
