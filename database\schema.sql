-- Le Prestine Authentication Database Schema
-- Execute this SQL in your Supabase SQL Editor

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone_number TEXT,
    country_code TEXT,
    country TEXT,
    verified BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER handle_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for user_profiles table
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Users can delete their own profile
CREATE POLICY "Users can delete own profile" ON public.user_profiles
    FOR DELETE USING (auth.uid() = id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_profiles_email_idx ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS user_profiles_country_idx ON public.user_profiles(country);
CREATE INDEX IF NOT EXISTS user_profiles_created_at_idx ON public.user_profiles(created_at);

-- Create a function to automatically create user profile on signup
-- This function runs with elevated privileges to bypass RLS
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, first_name, last_name)
    VALUES (NEW.id, COALESCE(NEW.email, ''), '', '');
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Could not create user profile for %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create a function to handle profile updates from the application
CREATE OR REPLACE FUNCTION public.update_user_profile(
    user_id UUID,
    new_first_name TEXT,
    new_last_name TEXT,
    new_phone_number TEXT DEFAULT NULL,
    new_country_code TEXT DEFAULT NULL,
    new_country TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        first_name = new_first_name,
        last_name = new_last_name,
        phone_number = new_phone_number,
        country_code = new_country_code,
        country = new_country,
        updated_at = NOW()
    WHERE id = user_id AND id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    discount TEXT,
    image_url TEXT NOT NULL,
    description TEXT,
    material TEXT,
    features TEXT[], -- Array of features
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create cart_items table
CREATE TABLE IF NOT EXISTS public.cart_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    product_id TEXT REFERENCES public.products(id) ON DELETE CASCADE NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(user_id, product_id) -- Prevent duplicate items for same user
);

-- Create updated_at trigger for products
CREATE TRIGGER handle_products_updated_at
    BEFORE UPDATE ON public.products
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Create updated_at trigger for cart_items
CREATE TRIGGER handle_cart_items_updated_at
    BEFORE UPDATE ON public.cart_items
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security for products and cart_items
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;

-- Products policies (public read access)
CREATE POLICY "Anyone can view products" ON public.products
    FOR SELECT USING (true);

-- Cart items policies (user-specific access)
CREATE POLICY "Users can view own cart items" ON public.cart_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cart items" ON public.cart_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cart items" ON public.cart_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own cart items" ON public.cart_items
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS products_category_idx ON public.products(category);
CREATE INDEX IF NOT EXISTS products_price_idx ON public.products(price);
CREATE INDEX IF NOT EXISTS cart_items_user_id_idx ON public.cart_items(user_id);
CREATE INDEX IF NOT EXISTS cart_items_product_id_idx ON public.cart_items(product_id);
CREATE INDEX IF NOT EXISTS cart_items_user_product_idx ON public.cart_items(user_id, product_id);

-- Create function to handle cart item upsert
CREATE OR REPLACE FUNCTION public.upsert_cart_item(
    p_user_id UUID,
    p_product_id TEXT,
    p_quantity INTEGER
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.cart_items (user_id, product_id, quantity)
    VALUES (p_user_id, p_product_id, p_quantity)
    ON CONFLICT (user_id, product_id)
    DO UPDATE SET
        quantity = cart_items.quantity + p_quantity,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.upsert_cart_item(UUID, TEXT, INTEGER) TO authenticated;

-- Insert sample products data
INSERT INTO public.products (id, name, category, price, original_price, discount, image_url, description, material, features) VALUES
('sp1', 'Abaya Shampoo', 'Laundry & Fabric Care', 24.99, NULL, NULL, '/assets/products/abaya-shampoo.jpg', 'Gentle formula specially designed for delicate fabrics like abayas', 'Gentle Formula for Delicate Fabrics', ARRAY['Gentle', 'Fabric Safe']),
('sp2', 'Premium Detergent Powder', 'Laundry & Fabric Care', 18.50, NULL, NULL, '/assets/products/detergent-powder.jpg', 'Concentrated cleaning formula for effective stain removal', 'Concentrated Cleaning Formula', ARRAY['Concentrated', 'Stain Removal']),
('sp3', 'Fabric Softener', 'Laundry & Fabric Care', 15.75, NULL, NULL, '/assets/products/fabric-softener.jpg', 'Premium fabric softener for ultimate comfort', 'Premium Softening Formula', ARRAY['Softening', 'Fresh Scent']),
('sp4', 'Stain Remover Spray', 'Laundry & Fabric Care', 12.99, NULL, NULL, '/assets/products/stain-remover.jpg', 'Powerful stain removal for tough stains', 'Advanced Stain Fighting Technology', ARRAY['Powerful', 'Quick Action']),
('sp5', 'Delicate Wash', 'Laundry & Fabric Care', 21.25, NULL, NULL, '/assets/products/delicate-wash.jpg', 'Gentle care for your most precious garments', 'Ultra-Gentle Cleaning Formula', ARRAY['Gentle', 'Color Protection']),
('sp6', 'Multi-Surface Cleaner', 'Home & Surface Care', 16.99, NULL, NULL, '/assets/products/multi-surface-cleaner.jpg', 'Versatile cleaner for all surfaces', 'All-Purpose Cleaning Solution', ARRAY['Versatile', 'Streak-Free']),
('sp7', 'Laundry Detergent', 'Laundry & Fabric Care', 22.50, NULL, NULL, '/assets/products/laundry-detergent.jpg', 'Advanced cleaning technology with color protection', 'Advanced Cleaning Technology', ARRAY['Advanced Formula', 'Color Protection']),
('sp8', 'Glass Cleaner', 'Home & Surface Care', 13.75, NULL, NULL, '/assets/products/glass-cleaner.jpg', 'Crystal clear results for all glass surfaces', 'Streak-Free Glass Formula', ARRAY['Streak-Free', 'Crystal Clear']),
('sp9', 'Floor Cleaner', 'Home & Surface Care', 19.99, NULL, NULL, '/assets/products/floor-cleaner.jpg', 'Deep cleaning power for all floor types', 'Multi-Floor Cleaning Formula', ARRAY['Deep Clean', 'Fresh Scent']),
('sp10', 'Bathroom Cleaner', 'Home & Surface Care', 17.50, NULL, NULL, '/assets/products/bathroom-cleaner.jpg', 'Powerful bathroom cleaning solution', 'Anti-Bacterial Formula', ARRAY['Anti-Bacterial', 'Lime Scale Removal'])
ON CONFLICT (id) DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO anon;
GRANT SELECT ON public.products TO anon, authenticated;
GRANT ALL ON public.cart_items TO authenticated;
