import React from 'react';
import AddressList from './AddressList';

interface AddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (addressId: string | null) => void;
  selectedAddressId: string | null;
}

const AddressModal: React.FC<AddressModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedAddressId
}) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 overflow-y-auto" 
      aria-labelledby="modal-title" 
      role="dialog" 
      aria-modal="true"
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-all duration-300"
        onClick={onClose}
        aria-hidden="true"
      ></div>

      {/* Modal */}
      <div className="relative min-h-[100dvh] flex items-center justify-center p-4 sm:p-6 md:p-8">
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-[90vw] md:max-w-5xl lg:max-w-6xl w-full mx-auto p-4 sm:p-6 md:p-8 overflow-hidden transform transition-all">
          {/* Header */}
          <div className="flex items-center justify-between mb-6 md:mb-8">
            <h2 
              className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 tracking-tight" 
              id="modal-title"
            >
              Select Delivery Address
            </h2>
            <button
              onClick={onClose}
              className="p-2 -m-2 text-gray-500 hover:text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-100"
              aria-label="Close modal"
            >
              <svg
                className="w-6 h-6 transform hover:scale-110 transition-transform duration-200"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="max-h-[calc(100vh-12rem)] sm:max-h-[calc(100vh-14rem)] md:max-h-[calc(100vh-16rem)] overflow-y-auto px-2">
            <AddressList
              onSelect={true}
              onAddressSelect={onSelect}
              selectedAddressId={selectedAddressId}
            />
          </div>

          {/* Footer */}
          <div className="mt-6 md:mt-8 flex justify-end space-x-4 border-t border-gray-100 pt-4 md:pt-6">
            <button
              onClick={onClose}
              className="px-4 sm:px-6 py-2.5 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 text-sm sm:text-base font-medium hover:border-gray-400"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                if (selectedAddressId) {
                  onClose();
                }
              }}
              disabled={!selectedAddressId}
              className="px-4 sm:px-6 py-2.5 bg-brand-accent-teal text-white rounded-lg hover:bg-brand-accent-teal-darker transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base font-medium transform hover:scale-[1.02] active:scale-[0.98]"
            >
              Confirm Selection
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressModal;
