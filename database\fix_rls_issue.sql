-- Modern Supabase Authentication Setup for Le Prestine
-- This follows 2025 best practices: Store data immediately + track verification status

-- Clean up any existing triggers (we don't need them anymore)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Add verified column to user_profiles table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'user_profiles'
        AND column_name = 'verified'
    ) THEN
        ALTER TABLE public.user_profiles
        ADD COLUMN verified BOOLEAN DEFAULT FALSE NOT NULL;

        -- Update existing users based on their email confirmation status
        UPDATE public.user_profiles
        SET verified = CASE
            WHEN EXISTS (
                SELECT 1 FROM auth.users
                WHERE auth.users.id = user_profiles.id
                AND auth.users.email_confirmed_at IS NOT NULL
            ) THEN TRUE
            ELSE FALSE
        END;

        RAISE NOTICE 'Added verified column and updated existing users';
    ELSE
        RAISE NOTICE 'Verified column already exists';
    END IF;
END $$;

-- Clean up any test users that aren't verified (optional - uncomment if needed)
-- DELETE FROM auth.users WHERE email_confirmed_at IS NULL;
-- DELETE FROM public.user_profiles WHERE verified = FALSE;

-- Create the database trigger function with SECURITY DEFINER to bypass RLS
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (
        id,
        email,
        first_name,
        last_name,
        phone_number,
        country_code,
        country,
        verified
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        NEW.raw_user_meta_data->>'phone_number',
        NEW.raw_user_meta_data->>'country_code',
        NEW.raw_user_meta_data->>'country',
        CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN TRUE ELSE FALSE END
    );
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Could not create user profile for %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- The new approach:
-- 1. User signs up -> profile created immediately with verified: false by trigger
-- 2. User clicks email verification link -> verified gets updated to true
-- 3. All user data is safely stored from the beginning via metadata
-- 4. Marketing can use all user data, filtering by verified status if needed

-- Create a secure function to update user profiles from the application
CREATE OR REPLACE FUNCTION public.update_user_profile(
    user_id UUID,
    new_first_name TEXT,
    new_last_name TEXT,
    new_phone_number TEXT DEFAULT NULL,
    new_country_code TEXT DEFAULT NULL,
    new_country TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        first_name = new_first_name,
        last_name = new_last_name,
        phone_number = new_phone_number,
        country_code = new_country_code,
        country = new_country,
        updated_at = NOW()
    WHERE id = user_id AND id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user profile (useful for debugging)
CREATE OR REPLACE FUNCTION public.get_user_profile(user_id UUID)
RETURNS TABLE(
    id UUID,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    country_code TEXT,
    country TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.email,
        p.first_name,
        p.last_name,
        p.phone_number,
        p.country_code,
        p.country,
        p.created_at,
        p.updated_at
    FROM public.user_profiles p
    WHERE p.id = user_id AND p.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the functions
GRANT EXECUTE ON FUNCTION public.update_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_profile TO authenticated;

-- Test the trigger by checking if it exists
SELECT
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'on_auth_user_created';

-- Verify the setup
SELECT 'Setup completed successfully! Trigger and functions are ready.' as status;

-- Create a secure function to update user profiles from the application
CREATE OR REPLACE FUNCTION public.update_user_profile(
    user_id UUID,
    new_first_name TEXT,
    new_last_name TEXT,
    new_phone_number TEXT DEFAULT NULL,
    new_country_code TEXT DEFAULT NULL,
    new_country TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        first_name = new_first_name,
        last_name = new_last_name,
        phone_number = new_phone_number,
        country_code = new_country_code,
        country = new_country,
        updated_at = NOW()
    WHERE id = user_id AND id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user profile (useful for debugging)
CREATE OR REPLACE FUNCTION public.get_user_profile(user_id UUID)
RETURNS TABLE(
    id UUID,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    country_code TEXT,
    country TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.email,
        p.first_name,
        p.last_name,
        p.phone_number,
        p.country_code,
        p.country,
        p.created_at,
        p.updated_at
    FROM public.user_profiles p
    WHERE p.id = user_id AND p.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the functions
GRANT EXECUTE ON FUNCTION public.update_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_profile TO authenticated;

-- Test the trigger by checking if it exists
SELECT
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'on_auth_user_created';

-- Verify the setup
SELECT 'Setup completed successfully! Trigger and functions are ready.' as status;
