import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase, UserProfile } from '../lib/supabase';

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData: Partial<UserProfile>) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: Error | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchUserProfile(session.user.id);
      }

      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.email_confirmed_at);

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Check if user just got verified
          if (event === 'TOKEN_REFRESHED' && session.user.email_confirmed_at) {
            console.log('User email verified, updating profile...');
            await updateUserVerificationStatus(session.user.id);
          }

          // Add a small delay for new signups to let the trigger complete
          if (event === 'SIGNED_IN') {
            console.log('User signed in, waiting for trigger to complete...');
            setTimeout(() => fetchUserProfile(session.user.id), 500);
          } else {
            await fetchUserProfile(session.user.id);
          }
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (userId: string, retryCount = 0) => {
    try {
      console.log(`Fetching profile for user: ${userId} (attempt ${retryCount + 1})`);

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Profile doesn't exist yet, might be trigger delay
          if (retryCount < 3) {
            console.log('Profile not found, retrying in 1 second...');
            setTimeout(() => fetchUserProfile(userId, retryCount + 1), 1000);
            return;
          } else {
            console.log('Profile not found after retries, user might need to complete signup');
            setProfile(null);
            return;
          }
        } else {
          console.error('Error fetching user profile:', error);
          return;
        }
      }

      console.log('Profile fetched successfully:', data);
      setProfile(data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  const updateUserVerificationStatus = async (userId: string) => {
    try {
      console.log('Updating verification status for user:', userId);

      const { error } = await supabase
        .from('user_profiles')
        .update({ verified: true })
        .eq('id', userId);

      if (error) {
        console.error('Error updating verification status:', error);
      } else {
        console.log('User verification status updated successfully');
      }
    } catch (error) {
      console.error('Error updating verification status:', error);
    }
  };

  const signUp = async (email: string, password: string, userData: Partial<UserProfile>) => {
    try {
      console.log('Starting signup process for:', email);
      console.log('User data:', userData);

      // Store user data in metadata for the trigger to use
      console.log('Storing metadata:', {
        first_name: userData.first_name || '',
        last_name: userData.last_name || '',
        phone_number: userData.phone_number || null,
        country_code: userData.country_code || null,
        country: userData.country || null,
      });

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.first_name || '',
            last_name: userData.last_name || '',
            phone_number: userData.phone_number || null,
            country_code: userData.country_code || null,
            country: userData.country || null,
          }
        }
      });

      if (error) {
        console.error('Supabase auth signup error:', error);
        return { error };
      }

      console.log('Signup successful! Profile will be created by database trigger.');

      return { error: null };
    } catch (error) {
      console.error('Unexpected error during signup:', error);
      return { error: error as AuthError };
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('No user logged in') };
    }

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        return { error: new Error(error.message) };
      }

      // Refresh profile data
      await fetchUserProfile(user.id);
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
