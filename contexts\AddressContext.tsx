import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import { Address, AddressFormData, AddressContextType } from '../types';

const AddressContext = createContext<AddressContextType | undefined>(undefined);

export function useAddress() {
  const context = useContext(AddressContext);
  if (context === undefined) {
    throw new Error('useAddress must be used within an AddressProvider');
  }
  return context;
}

export const AddressProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [defaultAddress, setDefaultAddressState] = useState<Address | null>(null);

  // Fetch addresses on mount and when user changes
  const fetchAddresses = async () => {
    if (!user) {
      setAddresses([]);
      setSelectedAddress(null);
      setDefaultAddressState(null);
      setLoading(false);
      return;
    }

    try {
      const { data, error: fetchError } = await supabase
        .from('user_addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;

      setAddresses(data);
      const defaultAddr = data.find(addr => addr.is_default);
      setDefaultAddressState(defaultAddr || null);
      if (!selectedAddress) {
        setSelectedAddress(defaultAddr || null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch addresses');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAddresses();
  }, [user]);

  const addAddress = async (data: AddressFormData) => {
    if (!user) throw new Error('User must be logged in');

    try {
      setLoading(true);
      setError(null);
      
      const { data: newAddress, error: insertError } = await supabase
        .from('user_addresses')
        .insert([{ ...data, user_id: user.id }])
        .select()
        .single();

      if (insertError) {
        setError(insertError.message);
        return null;
      }

      await fetchAddresses();
      return newAddress;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add address';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateAddress = async (id: string, data: AddressFormData) => {
    if (!user) throw new Error('User must be logged in');

    try {
      setLoading(true);
      const { error: updateError } = await supabase
        .from('user_addresses')
        .update(data)
        .eq('id', id)
        .eq('user_id', user.id);

      if (updateError) throw updateError;

      await fetchAddresses();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteAddress = async (id: string) => {
    if (!user) throw new Error('User must be logged in');

    try {
      setLoading(true);
      const { error: deleteError } = await supabase
        .from('user_addresses')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (deleteError) throw deleteError;

      if (selectedAddress?.id === id) {
        setSelectedAddress(null);
      }

      await fetchAddresses();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const makeAddressDefault = async (id: string) => {
    if (!user) throw new Error('User must be logged in');

    try {
      setLoading(true);
      setError(null);
      
      const { error: updateError } = await supabase
        .from('user_addresses')
        .update({ is_default: true })
        .eq('id', id)
        .eq('user_id', user.id);

      if (updateError) {
        setError(updateError.message);
        return;
      }

      await fetchAddresses();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set default address';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const selectAddress = (address: Address | null) => {
    setSelectedAddress(address);
  };

  const value: AddressContextType = {
    addresses,
    loading,
    error,
    selectedAddress,
    defaultAddress,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress: makeAddressDefault,
    selectAddress,
    refreshAddresses: fetchAddresses
  };

  return (
    <AddressContext.Provider value={value}>
      {children}
    </AddressContext.Provider>
  );
};
