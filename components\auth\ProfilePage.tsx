import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useAddress } from '../../contexts/AddressContext';
import { UserIcon, MailIcon, PhoneIcon, GlobeAltIcon, LogoutIcon, PencilIcon, MapPinIcon } from '../common/Icon';

const ProfilePage: React.FC = () => {
  const { user, profile, signOut, updateProfile, loading } = useAuth();
  const { addresses } = useAddress();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    countryCode: '',
    country: '',
  });

  useEffect(() => {
    setIsVisible(true);
    
    // Redirect if not logged in
    if (!loading && !user) {
      navigate('/login');
      return;
    }

    // Populate form data when profile is loaded
    if (profile) {
      setFormData({
        firstName: profile.first_name || '',
        lastName: profile.last_name || '',
        phoneNumber: profile.phone_number || '',
        countryCode: profile.country_code || '',
        country: profile.country || '',
      });
    }
  }, [user, profile, loading, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setUpdating(true);
    setError('');
    setMessage('');

    const updates = {
      first_name: formData.firstName.trim(),
      last_name: formData.lastName.trim(),
      phone_number: formData.phoneNumber.trim() || undefined,
      country_code: formData.countryCode || undefined,
      country: formData.country || undefined,
    };

    const { error } = await updateProfile(updates);

    if (error) {
      setError(error.message);
    } else {
      setMessage('Profile updated successfully!');
      setIsEditing(false);
    }

    setUpdating(false);
  };

  const handleSignOut = async () => {
    const { error } = await signOut();
    if (error) {
      setError(error.message);
    } else {
      navigate('/');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-brand-accent-teal/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-brand-main-red/10 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white/20 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-2xl mx-auto">
        {/* Header */}
        <div className={`text-center mb-8 transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <img 
            src="/assets/logo.png" 
            alt="Le Prestine Logo" 
            className="h-16 w-auto mx-auto mb-6"
          />
          <h2 className="text-3xl font-bold text-gray-900 font-serif">
            My Profile
          </h2>
          <p className="mt-2 text-gray-600">
            Manage your account information
          </p>
        </div>

        {/* Profile Card */}
        <div className={`
          bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8
          transition-all duration-1200 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}
        `} style={{ transitionDelay: '200ms' }}>
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-3xl pointer-events-none"></div>

          <div className="relative">
            {/* Messages */}
            {message && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-2xl text-sm mb-6">
                {message}
              </div>
            )}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl text-sm mb-6">
                {error}
              </div>
            )}

            {/* Profile Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-accent-teal to-brand-accent-teal-darker rounded-full flex items-center justify-center">
                  <UserIcon className="text-white" size={24} />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">
                    {profile?.first_name} {profile?.last_name}
                  </h3>
                  <p className="text-gray-600">{user.email}</p>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="flex items-center space-x-2 px-4 py-2 bg-brand-accent-teal text-white rounded-xl hover:bg-brand-accent-teal-darker transition-colors"
                >
                  <PencilIcon size={16} />
                  <span>{isEditing ? 'Cancel' : 'Edit'}</span>
                </button>
                
                <button
                  onClick={handleSignOut}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors"
                >
                  <LogoutIcon size={16} />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>

            {isEditing ? (
              /* Edit Form */
              <form onSubmit={handleUpdateProfile} className="space-y-6">
                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="group">
                    <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2">
                      First Name
                    </label>
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300"
                      placeholder="First name"
                    />
                  </div>

                  <div className="group">
                    <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2">
                      Last Name
                    </label>
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300"
                      placeholder="Last name"
                    />
                  </div>
                </div>

                {/* Phone Number */}
                <div className="group">
                  <label htmlFor="phoneNumber" className="block text-sm font-semibold text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    id="phoneNumber"
                    name="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300"
                    placeholder="Phone number"
                  />
                </div>

                {/* Country */}
                <div className="group">
                  <label htmlFor="country" className="block text-sm font-semibold text-gray-700 mb-2">
                    Country
                  </label>
                  <input
                    id="country"
                    name="country"
                    type="text"
                    value={formData.country}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300"
                    placeholder="Country"
                  />
                </div>

                {/* Update Button */}
                <button
                  type="submit"
                  disabled={updating}
                  className={`
                    w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                    text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl
                    transform hover:scale-105 active:scale-95 transition-all duration-300
                    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                  `}
                >
                  {updating ? 'Updating...' : 'Update Profile'}
                </button>
              </form>
            ) : (
              /* Profile Display */
              <div className="space-y-6">
                {/* Profile Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center space-x-3 p-4 bg-white/40 rounded-xl">
                    <MailIcon className="text-brand-accent-teal" size={20} />
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium text-gray-900">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-4 bg-white/40 rounded-xl">
                    <UserIcon className="text-brand-accent-teal" size={20} />
                    <div>
                      <p className="text-sm text-gray-600">Full Name</p>
                      <p className="font-medium text-gray-900">
                        {profile?.first_name} {profile?.last_name}
                      </p>
                    </div>
                  </div>

                  {profile?.phone_number && (
                    <div className="flex items-center space-x-3 p-4 bg-white/40 rounded-xl">
                      <PhoneIcon className="text-brand-accent-teal" size={20} />
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <p className="font-medium text-gray-900">
                          {profile.country_code} {profile.phone_number}
                        </p>
                      </div>
                    </div>
                  )}

                  {profile?.country && (
                    <div className="flex items-center space-x-3 p-4 bg-white/40 rounded-xl">
                      <GlobeAltIcon className="text-brand-accent-teal" size={20} />
                      <div>
                        <p className="text-sm text-gray-600">Country</p>
                        <p className="font-medium text-gray-900">{profile.country}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Account Management */}
                <div className="flex items-center justify-between py-4 px-6 bg-gradient-to-r from-brand-accent-teal/10 to-transparent rounded-xl">
                  <div className="flex items-center space-x-3">
                    <MapPinIcon className="text-brand-accent-teal" size={24} />
                    <div>
                      <h4 className="font-medium text-gray-900">Delivery Addresses ({addresses.length})</h4>
                      <p className="text-sm text-gray-600">
                        {addresses.length === 0 
                          ? "Add your delivery addresses"
                          : `Manage ${addresses.length} saved ${addresses.length === 1 ? 'address' : 'addresses'}`}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate('/addresses')}
                    className="px-4 py-2 bg-brand-accent-teal text-white rounded-lg hover:bg-brand-accent-teal-darker transition-colors"
                  >
                    Manage Addresses
                  </button>
                </div>

                {/* Account Info */}
                <div className="pt-6 border-t border-gray-200/50">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Account Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Member since</p>
                      <p className="font-medium text-gray-900">
                        {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Last updated</p>
                      <p className="font-medium text-gray-900">
                        {profile?.updated_at ? new Date(profile.updated_at).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
