-- Test script to debug profile creation issues
-- Run this in your Supabase SQL Editor to check the current state

-- 1. Check if the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- 2. Check if the function exists
SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_name IN ('handle_new_user', 'update_user_profile', 'get_user_profile');

-- 3. Check current RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'user_profiles';

-- 4. Check if user_profiles table exists and its structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
ORDER BY ordinal_position;

-- 5. Check current users in auth.users (first 5)
SELECT 
    id,
    email,
    created_at,
    email_confirmed_at
FROM auth.users 
ORDER BY created_at DESC 
LIMIT 5;

-- 6. Check current profiles in user_profiles (first 5)
SELECT 
    id,
    email,
    first_name,
    last_name,
    phone_number,
    country_code,
    country,
    created_at
FROM public.user_profiles 
ORDER BY created_at DESC 
LIMIT 5;

-- 7. Check for any users without profiles
SELECT 
    u.id,
    u.email,
    u.created_at as user_created,
    p.id as profile_id
FROM auth.users u
LEFT JOIN public.user_profiles p ON u.id = p.id
WHERE p.id IS NULL
ORDER BY u.created_at DESC;
