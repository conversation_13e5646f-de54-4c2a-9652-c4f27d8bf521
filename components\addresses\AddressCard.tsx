import React from 'react';
import { Address } from '../../types';

interface AddressCardProps {
  address: Address;
  onEdit: () => void;
  onDelete: () => void;
  onSetDefault: () => void;
  isDefault: boolean;
  isSelected?: boolean;
  onSelect?: () => void;
}

const AddressCard: React.FC<AddressCardProps> = ({
  address,
  onEdit,
  onDelete,
  onSetDefault,
  isDefault,
  isSelected,
  onSelect
}) => {
  return (
    <div
      onClick={onSelect}
      className={`relative bg-white rounded-xl border-2 transition-all duration-300 ${
        isSelected
          ? 'border-brand-accent-teal shadow-lg scale-[1.02]'
          : 'border-gray-200 hover:border-brand-accent-teal/50'
      } p-6 ${onSelect ? 'cursor-pointer' : ''}`}
    >
      {/* Address Type Badge */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-2">
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${
              address.address_type === 'home'
                ? 'bg-blue-100 text-blue-700'
                : address.address_type === 'work'
                ? 'bg-purple-100 text-purple-700'
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {address.address_type}
          </span>
          {isDefault && (
            <span className="px-3 py-1 bg-brand-accent-teal/10 text-brand-accent-teal rounded-full text-sm font-medium">
              Default
            </span>
          )}
        </div>

        {/* Actions Menu */}
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="p-2 text-gray-600 hover:text-brand-accent-teal hover:bg-brand-accent-teal/10 rounded-lg transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </button>
          {!isDefault && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSetDefault();
              }}
              className="p-2 text-gray-600 hover:text-brand-accent-teal hover:bg-brand-accent-teal/10 rounded-lg transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </button>
          )}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
            aria-label="Delete address"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Address Details */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900">{address.full_name}</h3>
        <p className="text-gray-600">{address.phone_number}</p>
        <div className="text-gray-600 space-y-1">
          <p>{address.street_address}</p>
          {address.apartment && <p>{address.apartment}</p>}
          <p>
            {address.city}, {address.state} {address.postal_code}
          </p>
          <p>{address.country}</p>
        </div>
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-3 right-3">
          <div className="w-6 h-6 bg-brand-accent-teal text-white rounded-full flex items-center justify-center">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressCard;
