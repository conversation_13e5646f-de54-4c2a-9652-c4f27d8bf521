import React, { useState } from 'react';
import { useAddress } from '../../contexts/AddressContext';
import { AddressFormData } from '../../types';
import AddressCard from './AddressCard';
import AddressForm from './AddressForm';
import ConfirmationModal from '../common/ConfirmationModal';

interface AddressListProps {
  onSelect?: boolean;
  onAddressSelect?: (addressId: string | null) => void;
  selectedAddressId?: string | null;
}

const AddressList: React.FC<AddressListProps> = ({
  onSelect = false,
  onAddressSelect,
  selectedAddressId
}) => {
  const {
    addresses,
    loading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    defaultAddress
  } = useAddress();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<{ id: string; data: AddressFormData } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);

  const handleAddSubmit = async (data: AddressFormData) => {
    setIsSubmitting(true);
    const newAddress = await addAddress(data);
    
    if (newAddress) {
      setShowAddForm(false);
    } else {
      alert('Failed to add address. Please try again.');
    }
    setIsSubmitting(false);
  };

  const handleEditSubmit = async (data: AddressFormData) => {
    if (!editingAddress) return;

    try {
      setIsSubmitting(true);
      await updateAddress(editingAddress.id, data);
      setEditingAddress(null);
    } catch (err) {
      console.error('Failed to update address:', err);
      alert('Failed to update address. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClick = (id: string) => {
    setAddressToDelete(id);
    setDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!addressToDelete) return;

    try {
      await deleteAddress(addressToDelete);
      if (onAddressSelect && selectedAddressId === addressToDelete) {
        onAddressSelect(null);
      }
      setDeleteModalOpen(false);
      setAddressToDelete(null);
    } catch (err) {
      console.error('Failed to delete address:', err);
      alert('Failed to delete address. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-brand-accent-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading addresses...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 text-red-600 p-4 rounded-lg inline-block">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setAddressToDelete(null);
        }}
        onConfirm={handleDelete}
        title="Delete Address"
        message="Are you sure you want to delete this address? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
      {/* Address List */}
      {addresses.length === 0 && !showAddForm ? (
        <div className="text-center py-12 bg-gray-50 rounded-xl">
          <svg
            className="w-16 h-16 text-gray-400 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No addresses found
          </h3>
          <p className="text-gray-600 mb-4">
            Add your first delivery address to continue
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-brand-accent-teal hover:bg-brand-accent-teal-darker focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-accent-teal"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            Add New Address
          </button>
        </div>
      ) : (
        <>
          {/* Add Address Button */}
          {!showAddForm && !editingAddress && (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full py-4 px-6 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-brand-accent-teal hover:text-brand-accent-teal transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <span>Add New Address</span>
            </button>
          )}

          {/* Add/Edit Form */}
          {(showAddForm || editingAddress) && (
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                {editingAddress ? 'Edit Address' : 'Add New Address'}
              </h3>
              <AddressForm
                initialData={editingAddress?.data}
                onSubmit={editingAddress ? handleEditSubmit : handleAddSubmit}
                onCancel={() => {
                  setShowAddForm(false);
                  setEditingAddress(null);
                }}
                isSubmitting={isSubmitting}
              />
            </div>
          )}

          {/* Address Grid */}
          {!showAddForm && !editingAddress && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {addresses.map((address) => (
                <AddressCard
                  key={address.id}
                  address={address}
                  isDefault={address.id === defaultAddress?.id}
                  isSelected={onSelect && address.id === selectedAddressId}
                  onSelect={
                    onSelect && onAddressSelect
                      ? () => onAddressSelect(address.id)
                      : undefined
                  }
                  onEdit={() =>
                    setEditingAddress({
                      id: address.id,
                      data: {
                        address_type: address.address_type,
                        full_name: address.full_name,
                        phone_number: address.phone_number,
                        street_address: address.street_address,
                        apartment: address.apartment || '',
                        city: address.city,
                        state: address.state,
                        postal_code: address.postal_code,
                        country: address.country
                      }
                    })
                  }
                  onDelete={() => handleDeleteClick(address.id)}
                  onSetDefault={() => setDefaultAddress(address.id)}
                />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AddressList;
