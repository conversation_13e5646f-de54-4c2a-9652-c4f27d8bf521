-- Create user_addresses table
CREATE TABLE public.user_addresses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    address_type TEXT NOT NULL, -- 'home', 'work', etc.
    full_name TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    street_address TEXT NOT NULL,
    apartment TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    postal_code TEXT NOT NULL,
    country TEXT NOT NULL,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    CONSTRAINT unique_default_address UNIQUE (user_id, is_default)
);

-- Add updated_at trigger
CREATE TRIGGER handle_addresses_updated_at
    BEFORE UPDATE ON public.user_addresses
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable RLS
ALTER TABLE public.user_addresses ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own addresses"
ON public.user_addresses FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can create own addresses"
ON public.user_addresses FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own addresses"
ON public.user_addresses FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own addresses"
ON public.user_addresses FOR DELETE
USING (auth.uid() = user_id);

-- Create function to handle setting default address
CREATE OR REPLACE FUNCTION public.set_default_address(
    p_user_id UUID,
    p_address_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- First, remove default status from all other addresses
    UPDATE public.user_addresses
    SET is_default = false
    WHERE user_id = p_user_id;
    
    -- Then set the specified address as default
    UPDATE public.user_addresses
    SET is_default = true
    WHERE id = p_address_id AND user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON public.user_addresses TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_default_address TO authenticated;
