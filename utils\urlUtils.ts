// Utility functions for converting between product names, IDs, and URL slugs

// Convert product name to URL-friendly slug
export const createProductSlug = (productName: string): string => {
  return productName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Product ID to slug mapping
export const productIdToSlug: { [key: string]: string } = {
  'sp1': 'abaya-shampoo',
  'sp2': 'premium-detergent-powder',
  'sp3': 'dishwash-liquid',
  'sp4': 'professional-emulsifiers',
  'sp5': 'fabric-softener',
  'sp6': 'gentle-hand-wash',
  'sp7': 'laundry-detergent',
  'sp8': 'taze-fresh-cleaner',
  'sp9': 'toilet-cleaner'
};

// Slug to product ID mapping (reverse lookup)
export const productSlugToId: { [key: string]: string } = Object.fromEntries(
  Object.entries(productIdToSlug).map(([id, slug]) => [slug, id])
);

// Convert product ID to slug
export const getProductSlug = (productId: string): string => {
  return productIdToSlug[productId] || productId;
};

// Convert slug to product ID
export const getProductIdFromSlug = (slug: string): string => {
  return productSlugToId[slug] || slug;
};

// Get product URL from product ID
export const getProductUrl = (productId: string): string => {
  const slug = getProductSlug(productId);
  return `/product/${slug}`;
};

// Validate if a slug exists
export const isValidProductSlug = (slug: string): boolean => {
  return slug in productSlugToId;
};
