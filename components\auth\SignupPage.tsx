import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { EyeIcon, EyeSlashIcon, MailIcon, LockClosedIcon, UserIcon, GlobeAltIcon } from '../common/Icon';
import PhoneInput from '../common/PhoneInput';
import CountrySelect from '../common/CountrySelect';

// Add notification popup styles
const NotificationPopup: React.FC<{ message: string; onClose: () => void }> = ({ message, onClose }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
      <div className="bg-white rounded-3xl p-8 max-w-md w-full mx-4 relative transform transition-all scale-100 animate-fade-in shadow-2xl border border-gray-100">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <MailIcon className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Email Verification Required</h3>
          <p className="text-sm text-gray-500 mb-6">{message}</p>
          <button
            onClick={onClose}
            className="w-full inline-flex justify-center rounded-2xl border border-transparent shadow-sm px-4 py-3 bg-brand-main-red text-base font-medium text-white hover:bg-brand-main-red-darker focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-main-red sm:text-sm transition-all duration-300"
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

const SignupPage: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    countryCode: '+1',
    country: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [showNotification, setShowNotification] = useState(false);

  const { signUp, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    setIsVisible(true);
    // Redirect if already logged in
    if (user) {
      navigate('/profile');
    }
  }, [user, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      setError('First name and last name are required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    const userData = {
      first_name: formData.firstName.trim(),
      last_name: formData.lastName.trim(),
      phone_number: formData.phoneNumber.trim() || undefined,
      country_code: formData.countryCode,
      country: formData.country || undefined,
    };

    const { error } = await signUp(formData.email, formData.password, userData);

    if (error) {
      setError(error.message);
      setLoading(false);
    } else {
      // Show notification popup
      setShowNotification(true);
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-brand-accent-teal/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-brand-main-red/10 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white/20 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-2xl mx-auto">
        {/* Logo and Header */}
        <div className={`text-center mb-8 transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <img 
            src="/assets/logo.png" 
            alt="Le Prestine Logo" 
            className="h-16 w-auto mx-auto mb-6"
          />
          <h2 className="text-3xl font-bold text-gray-900 font-serif">
            Join Le Prestine
          </h2>
          <p className="mt-2 text-gray-600">
            Create your account to get started
          </p>
        </div>

        {/* Signup Form */}
        <div className={`
          bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8
          transition-all duration-1200 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}
        `} style={{ transitionDelay: '200ms' }}>
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-3xl pointer-events-none"></div>

          <form onSubmit={handleSubmit} className="space-y-6 relative">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl text-sm">
                {error}
              </div>
            )}

            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group">
                <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  First Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <UserIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="First name"
                  />
                </div>
              </div>

              <div className="group">
                <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Last Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <UserIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="Last name"
                  />
                </div>
              </div>
            </div>

            {/* Email Field */}
            <div className="group">
              <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                Email Address *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <MailIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Phone Number Field */}
            <PhoneInput
              value={formData.phoneNumber}
              onChange={(value) => setFormData(prev => ({ ...prev, phoneNumber: value }))}
              countryCode={formData.countryCode}
              onCountryChange={(code) => setFormData(prev => ({ ...prev, countryCode: code }))}
              placeholder="Phone number"
              className="group"
            />

            {/* Country Field */}
              <CountrySelect 
                value={formData.country}
                onChange={(country, countryCode) => {
                  setFormData(prev => ({
                    ...prev,
                    country,
                    countryCode,
                    phoneNumber: '' // Reset phone number when country changes
                  }));
                }}
                required
                placeholder="Select your country"
                className="group"
              />

            {/* Password Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group">
                <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <LockClosedIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-12 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="Password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    ) : (
                      <EyeIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    )}
                  </button>
                </div>
              </div>

              <div className="group">
                <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Confirm Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <LockClosedIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-12 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="Confirm password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    ) : (
                      <EyeIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className={`
                group relative w-full overflow-hidden bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                text-white font-bold py-4 px-8 rounded-2xl shadow-2xl hover:shadow-3xl
                transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-brand-main-red/30
                transition-all duration-500 ease-out text-lg border border-white/20
                disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
              `}
            >
              {/* Shimmer Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
              
              <span className="relative">
                {loading ? 'Creating Account...' : 'Create Account'}
              </span>
            </button>

            {/* Sign In Link */}
            <div className="text-center">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link 
                  to="/login" 
                  className="font-semibold text-brand-accent-teal hover:text-brand-accent-teal-darker transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
      
      {/* Notification Popup */}
      {showNotification && (
        <NotificationPopup
          message="We've sent a verification link to your email address. Please check your inbox and click the link to verify your account."
          onClose={() => {
            setShowNotification(false);
            navigate('/login', {
              state: { message: 'Account created successfully! Your data has been saved. Please check your email to verify your account.' }
            });
          }}
        />
      )}
    </div>
  );
};

export default SignupPage;
