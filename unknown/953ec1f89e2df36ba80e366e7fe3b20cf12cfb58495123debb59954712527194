import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { EyeIcon, EyeSlashIcon, MailIcon, LockClosedIcon } from '../common/Icon';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  const { signIn, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    setIsVisible(true);
    // Redirect if already logged in
    if (user) {
      navigate('/profile');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const { error } = await signIn(email, password);

    if (error) {
      setError(error.message);
    } else {
      navigate('/profile');
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-brand-accent-teal/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-brand-main-red/10 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white/20 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-md mx-auto">
        {/* Logo and Header */}
        <div className={`text-center mb-8 transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <img 
            src="/assets/logo.png" 
            alt="Le Prestine Logo" 
            className="h-16 w-auto mx-auto mb-6"
          />
          <h2 className="text-3xl font-bold text-gray-900 font-serif">
            Welcome Back
          </h2>
          <p className="mt-2 text-gray-600">
            Sign in to your Le Prestine account
          </p>
        </div>

        {/* Login Form */}
        <div className={`
          bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8
          transition-all duration-1200 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}
        `} style={{ transitionDelay: '200ms' }}>
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-3xl pointer-events-none"></div>

          <form onSubmit={handleSubmit} className="space-y-6 relative">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl text-sm">
                {error}
              </div>
            )}

            {/* Email Field */}
            <div className="group">
              <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <MailIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                  placeholder="Enter your email"
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Password Field */}
            <div className="group">
              <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <LockClosedIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-12 pr-12 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-4 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                  ) : (
                    <EyeIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                  )}
                </button>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className={`
                group relative w-full overflow-hidden bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                text-white font-bold py-4 px-8 rounded-2xl shadow-2xl hover:shadow-3xl
                transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-brand-main-red/30
                transition-all duration-500 ease-out text-lg border border-white/20
                disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
              `}
            >
              {/* Shimmer Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
              
              <span className="relative">
                {loading ? 'Signing In...' : 'Sign In'}
              </span>
            </button>

            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-gray-600">
                Don't have an account?{' '}
                <Link 
                  to="/signup" 
                  className="font-semibold text-brand-accent-teal hover:text-brand-accent-teal-darker transition-colors"
                >
                  Sign up here
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
