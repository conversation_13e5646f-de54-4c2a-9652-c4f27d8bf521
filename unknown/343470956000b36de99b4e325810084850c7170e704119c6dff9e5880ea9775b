// IP Geolocation service for automatic country detection
export interface GeolocationData {
  country_code2: string;
  country_name: string;
  city: string;
  state_prov: string;
  zipcode: string;
  latitude: number;
  longitude: number;
  timezone: string;
  calling_code: string;
}

export interface GeolocationResponse {
  success: boolean;
  data?: GeolocationData;
  error?: string;
}

class GeolocationService {
  private cache: Map<string, GeolocationData> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Get user's location data from IP address using multiple fallback services
   */
  async getUserLocation(): Promise<GeolocationResponse> {
    try {
      // Check cache first
      const cachedData = this.getCachedData('user_location');
      if (cachedData) {
        return { success: true, data: cachedData };
      }

      // Try multiple services in order of preference
      const services = [
        this.getLocationFromIPAPI,
        this.getLocationFromIPGeolocation,
        this.getLocationFromIPInfo,
        this.getLocationFromFreeGeoIP
      ];

      for (const service of services) {
        try {
          const result = await service.call(this);
          if (result.success && result.data) {
            // Cache the successful result
            this.setCachedData('user_location', result.data);
            return result;
          }
        } catch (error) {
          console.warn('Geolocation service failed:', error);
          continue;
        }
      }

      // If all services fail, return a default fallback
      return this.getFallbackLocation();
    } catch (error) {
      console.error('Error getting user location:', error);
      return this.getFallbackLocation();
    }
  }

  /**
   * Primary service: ipapi.co (free, no API key required)
   */
  private async getLocationFromIPAPI(): Promise<GeolocationResponse> {
    try {
      const response = await fetch('https://ipapi.co/json/', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.reason || 'API error');
      }

      return {
        success: true,
        data: {
          country_code2: data.country_code || data.country || 'US',
          country_name: data.country_name || 'United States',
          city: data.city || '',
          state_prov: data.region || '',
          zipcode: data.postal || '',
          latitude: parseFloat(data.latitude) || 0,
          longitude: parseFloat(data.longitude) || 0,
          timezone: data.timezone || '',
          calling_code: data.country_calling_code || '+1'
        }
      };
    } catch (error) {
      console.warn('ipapi.co failed:', error);
      throw error;
    }
  }

  /**
   * Secondary service: ipgeolocation.io (free tier available)
   */
  private async getLocationFromIPGeolocation(): Promise<GeolocationResponse> {
    try {
      const response = await fetch('https://api.ipgeolocation.io/ipgeo?apiKey=free', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: {
          country_code2: data.country_code2 || 'US',
          country_name: data.country_name || 'United States',
          city: data.city || '',
          state_prov: data.state_prov || '',
          zipcode: data.zipcode || '',
          latitude: parseFloat(data.latitude) || 0,
          longitude: parseFloat(data.longitude) || 0,
          timezone: data.time_zone?.name || '',
          calling_code: data.calling_code || '+1'
        }
      };
    } catch (error) {
      console.warn('ipgeolocation.io failed:', error);
      throw error;
    }
  }

  /**
   * Third service: ipinfo.io (free tier available)
   */
  private async getLocationFromIPInfo(): Promise<GeolocationResponse> {
    try {
      const response = await fetch('https://ipinfo.io/json', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      const [lat, lng] = (data.loc || '0,0').split(',').map(parseFloat);

      return {
        success: true,
        data: {
          country_code2: data.country || 'US',
          country_name: this.getCountryNameFromCode(data.country) || 'United States',
          city: data.city || '',
          state_prov: data.region || '',
          zipcode: data.postal || '',
          latitude: lat || 0,
          longitude: lng || 0,
          timezone: data.timezone || '',
          calling_code: this.getCallingCodeFromCountry(data.country) || '+1'
        }
      };
    } catch (error) {
      console.warn('ipinfo.io failed:', error);
      throw error;
    }
  }

  /**
   * Fourth service: freegeoip.app (backup)
   */
  private async getLocationFromFreeGeoIP(): Promise<GeolocationResponse> {
    try {
      const response = await fetch('https://freegeoip.app/json/', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return {
        success: true,
        data: {
          country_code2: data.country_code || 'US',
          country_name: data.country_name || 'United States',
          city: data.city || '',
          state_prov: data.region_name || '',
          zipcode: data.zip_code || '',
          latitude: parseFloat(data.latitude) || 0,
          longitude: parseFloat(data.longitude) || 0,
          timezone: data.time_zone || '',
          calling_code: this.getCallingCodeFromCountry(data.country_code) || '+1'
        }
      };
    } catch (error) {
      console.warn('freegeoip.app failed:', error);
      throw error;
    }
  }

  /**
   * Fallback location (defaults to US)
   */
  private getFallbackLocation(): GeolocationResponse {
    return {
      success: true,
      data: {
        country_code2: 'US',
        country_name: 'United States',
        city: '',
        state_prov: '',
        zipcode: '',
        latitude: 0,
        longitude: 0,
        timezone: '',
        calling_code: '+1'
      }
    };
  }

  /**
   * Cache management
   */
  private getCachedData(key: string): GeolocationData | null {
    const expiry = this.cacheExpiry.get(key);
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return null;
    }
    return this.cache.get(key) || null;
  }

  private setCachedData(key: string, data: GeolocationData): void {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION);
  }

  /**
   * Helper methods
   */
  private getCountryNameFromCode(code: string): string {
    const countryNames: { [key: string]: string } = {
      'US': 'United States', 'GB': 'United Kingdom', 'CA': 'Canada',
      'AU': 'Australia', 'DE': 'Germany', 'FR': 'France', 'IN': 'India',
      'CN': 'China', 'JP': 'Japan', 'KR': 'South Korea', 'BR': 'Brazil',
      'MX': 'Mexico', 'IT': 'Italy', 'ES': 'Spain', 'NL': 'Netherlands',
      'SE': 'Sweden', 'NO': 'Norway', 'DK': 'Denmark', 'FI': 'Finland',
      'CH': 'Switzerland', 'AT': 'Austria', 'BE': 'Belgium', 'IE': 'Ireland',
      'PT': 'Portugal', 'GR': 'Greece', 'PL': 'Poland', 'CZ': 'Czech Republic',
      'HU': 'Hungary', 'RO': 'Romania', 'BG': 'Bulgaria', 'HR': 'Croatia',
      'SK': 'Slovakia', 'SI': 'Slovenia', 'EE': 'Estonia', 'LV': 'Latvia',
      'LT': 'Lithuania', 'RU': 'Russia', 'UA': 'Ukraine', 'TR': 'Turkey',
      'SA': 'Saudi Arabia', 'AE': 'United Arab Emirates', 'IL': 'Israel',
      'EG': 'Egypt', 'ZA': 'South Africa', 'NG': 'Nigeria', 'KE': 'Kenya',
      'GH': 'Ghana', 'TH': 'Thailand', 'VN': 'Vietnam', 'MY': 'Malaysia',
      'SG': 'Singapore', 'PH': 'Philippines', 'ID': 'Indonesia', 'PK': 'Pakistan',
      'BD': 'Bangladesh', 'LK': 'Sri Lanka', 'NZ': 'New Zealand'
    };
    return countryNames[code] || code;
  }

  private getCallingCodeFromCountry(code: string): string {
    const callingCodes: { [key: string]: string } = {
      'US': '+1', 'CA': '+1', 'GB': '+44', 'AU': '+61', 'DE': '+49',
      'FR': '+33', 'IN': '+91', 'CN': '+86', 'JP': '+81', 'KR': '+82',
      'BR': '+55', 'MX': '+52', 'IT': '+39', 'ES': '+34', 'NL': '+31',
      'SE': '+46', 'NO': '+47', 'DK': '+45', 'FI': '+358', 'CH': '+41',
      'AT': '+43', 'BE': '+32', 'IE': '+353', 'PT': '+351', 'GR': '+30',
      'PL': '+48', 'CZ': '+420', 'HU': '+36', 'RO': '+40', 'BG': '+359',
      'HR': '+385', 'SK': '+421', 'SI': '+386', 'EE': '+372', 'LV': '+371',
      'LT': '+370', 'RU': '+7', 'UA': '+380', 'TR': '+90', 'SA': '+966',
      'AE': '+971', 'IL': '+972', 'EG': '+20', 'ZA': '+27', 'NG': '+234',
      'KE': '+254', 'GH': '+233', 'TH': '+66', 'VN': '+84', 'MY': '+60',
      'SG': '+65', 'PH': '+63', 'ID': '+62', 'PK': '+92', 'BD': '+880',
      'LK': '+94', 'NZ': '+64'
    };
    return callingCodes[code] || '+1';
  }
}

// Export singleton instance
export const geolocationService = new GeolocationService();
