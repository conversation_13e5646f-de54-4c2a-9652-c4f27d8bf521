import { CountryData, getCountryByDialCode } from '../data/countryCodes';

export interface PhoneValidationResult {
  isValid: boolean;
  formattedNumber?: string;
  country?: CountryData;
  error?: string;
}

export class PhoneNumberValidator {
  /**
   * Validate a phone number for a specific country
   */
  static validatePhoneNumber(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    if (!phoneNumber || !countryData) {
      return {
        isValid: false,
        error: 'Phone number and country data are required'
      };
    }

    // Clean the phone number (remove spaces, dashes, parentheses)
    const cleanNumber = this.cleanPhoneNumber(phoneNumber);

    // Check if number is empty after cleaning
    if (!cleanNumber) {
      return {
        isValid: false,
        error: 'Phone number cannot be empty'
      };
    }

    // Check if number contains only digits
    if (!/^\d+$/.test(cleanNumber)) {
      return {
        isValid: false,
        error: 'Phone number must contain only digits'
      };
    }

    // Check length constraints
    if (cleanNumber.length < countryData.minLength) {
      return {
        isValid: false,
        error: `Phone number is too short. Minimum ${countryData.minLength} digits required for ${countryData.name}`
      };
    }

    if (cleanNumber.length > countryData.maxLength) {
      return {
        isValid: false,
        error: `Phone number is too long. Maximum ${countryData.maxLength} digits allowed for ${countryData.name}`
      };
    }

    // Apply country-specific validation patterns
    const patternValidation = this.validateWithPattern(cleanNumber, countryData);
    if (!patternValidation.isValid) {
      return patternValidation;
    }

    // Format the number
    const formattedNumber = this.formatPhoneNumber(cleanNumber, countryData);

    return {
      isValid: true,
      formattedNumber,
      country: countryData
    };
  }

  /**
   * Auto-detect country from phone number
   */
  static detectCountryFromNumber(phoneNumber: string): CountryData | null {
    const cleanNumber = this.cleanPhoneNumber(phoneNumber);
    
    // Try to match with dial codes (longest first to avoid conflicts)
    const sortedCountries = [...require('../data/countryCodes').countryCodesData]
      .sort((a, b) => b.dialCode.length - a.dialCode.length);

    for (const country of sortedCountries) {
      const dialCodeDigits = country.dialCode.replace('+', '');
      if (cleanNumber.startsWith(dialCodeDigits)) {
        return country;
      }
    }

    return null;
  }

  /**
   * Clean phone number by removing non-digit characters
   */
  static cleanPhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/[^\d]/g, '');
  }

  /**
   * Format phone number according to country standards
   */
  static formatPhoneNumber(phoneNumber: string, countryData: CountryData): string {
    const cleanNumber = this.cleanPhoneNumber(phoneNumber);
    
    // Apply country-specific formatting
    switch (countryData.code) {
      case 'US':
      case 'CA':
        return this.formatNorthAmericanNumber(cleanNumber);
      case 'GB':
        return this.formatUKNumber(cleanNumber);
      case 'DE':
        return this.formatGermanNumber(cleanNumber);
      case 'FR':
        return this.formatFrenchNumber(cleanNumber);
      case 'IN':
        return this.formatIndianNumber(cleanNumber);
      case 'AU':
        return this.formatAustralianNumber(cleanNumber);
      default:
        return this.formatGenericNumber(cleanNumber);
    }
  }

  /**
   * Validate with country-specific patterns
   */
  private static validateWithPattern(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    // Country-specific validation rules
    switch (countryData.code) {
      case 'US':
      case 'CA':
        return this.validateNorthAmericanNumber(phoneNumber, countryData);
      case 'GB':
        return this.validateUKNumber(phoneNumber, countryData);
      case 'IN':
        return this.validateIndianNumber(phoneNumber, countryData);
      case 'CN':
        return this.validateChineseNumber(phoneNumber, countryData);
      default:
        return { isValid: true, country: countryData };
    }
  }

  // Country-specific validation methods
  private static validateNorthAmericanNumber(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    // North American numbers: area code cannot start with 0 or 1
    if (phoneNumber.length === 10) {
      const areaCode = phoneNumber.substring(0, 3);
      if (areaCode.startsWith('0') || areaCode.startsWith('1')) {
        return {
          isValid: false,
          error: 'Invalid area code. Area code cannot start with 0 or 1'
        };
      }
    }
    return { isValid: true, country: countryData };
  }

  private static validateUKNumber(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    // UK mobile numbers typically start with 7
    if (phoneNumber.length === 10 && !phoneNumber.startsWith('7')) {
      return {
        isValid: false,
        error: 'UK mobile numbers typically start with 7'
      };
    }
    return { isValid: true, country: countryData };
  }

  private static validateIndianNumber(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    // Indian mobile numbers start with 6, 7, 8, or 9
    if (phoneNumber.length === 10) {
      const firstDigit = phoneNumber.charAt(0);
      if (!['6', '7', '8', '9'].includes(firstDigit)) {
        return {
          isValid: false,
          error: 'Indian mobile numbers must start with 6, 7, 8, or 9'
        };
      }
    }
    return { isValid: true, country: countryData };
  }

  private static validateChineseNumber(phoneNumber: string, countryData: CountryData): PhoneValidationResult {
    // Chinese mobile numbers start with 1
    if (phoneNumber.length === 11 && !phoneNumber.startsWith('1')) {
      return {
        isValid: false,
        error: 'Chinese mobile numbers must start with 1'
      };
    }
    return { isValid: true, country: countryData };
  }

  // Formatting methods
  private static formatNorthAmericanNumber(phoneNumber: string): string {
    if (phoneNumber.length === 10) {
      return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
    }
    return phoneNumber;
  }

  private static formatUKNumber(phoneNumber: string): string {
    if (phoneNumber.length === 10) {
      return `${phoneNumber.substring(0, 4)} ${phoneNumber.substring(4, 7)} ${phoneNumber.substring(7)}`;
    }
    return phoneNumber;
  }

  private static formatGermanNumber(phoneNumber: string): string {
    if (phoneNumber.length >= 10) {
      return `${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 6)} ${phoneNumber.substring(6)}`;
    }
    return phoneNumber;
  }

  private static formatFrenchNumber(phoneNumber: string): string {
    if (phoneNumber.length === 9) {
      return `${phoneNumber.substring(0, 2)} ${phoneNumber.substring(2, 4)} ${phoneNumber.substring(4, 6)} ${phoneNumber.substring(6, 8)} ${phoneNumber.substring(8)}`;
    }
    return phoneNumber;
  }

  private static formatIndianNumber(phoneNumber: string): string {
    if (phoneNumber.length === 10) {
      return `${phoneNumber.substring(0, 5)} ${phoneNumber.substring(5)}`;
    }
    return phoneNumber;
  }

  private static formatAustralianNumber(phoneNumber: string): string {
    if (phoneNumber.length === 9) {
      return `${phoneNumber.substring(0, 4)} ${phoneNumber.substring(4, 7)} ${phoneNumber.substring(7)}`;
    }
    return phoneNumber;
  }

  private static formatGenericNumber(phoneNumber: string): string {
    // Generic formatting: add spaces every 3-4 digits
    if (phoneNumber.length <= 6) {
      return phoneNumber;
    } else if (phoneNumber.length <= 10) {
      const mid = Math.ceil(phoneNumber.length / 2);
      return `${phoneNumber.substring(0, mid)} ${phoneNumber.substring(mid)}`;
    } else {
      return `${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 6)} ${phoneNumber.substring(6)}`;
    }
  }

  /**
   * Get example phone number for a country
   */
  static getExampleNumber(countryData: CountryData): string {
    const examples: { [key: string]: string } = {
      'US': '(*************',
      'CA': '(*************',
      'GB': '7700 900123',
      'AU': '0412 345 678',
      'DE': '030 12345678',
      'FR': '01 23 45 67 89',
      'IN': '98765 43210',
      'CN': '138 0013 8000',
      'JP': '090-1234-5678',
      'KR': '010-1234-5678',
      'BR': '(11) 99999-9999',
      'MX': '55 1234 5678',
      'IT': '************',
      'ES': '612 34 56 78',
      'NL': '06 12345678',
      'SE': '070-123 45 67',
      'NO': '412 34 567',
      'DK': '20 12 34 56',
      'FI': '************'
    };

    return examples[countryData.code] || `${countryData.dialCode} 123456789`;
  }
}
