// Complete list of country codes and phone number data
export interface CountryData {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
  minLength: number;
  maxLength: number;
  pattern?: RegExp;
}

export const countryCodesData: CountryData[] = [
  { code: 'AF', name: 'Afghanistan', dialCode: '+93', flag: '🇦🇫', minLength: 8, maxLength: 9 },
  { code: 'AL', name: 'Albania', dialCode: '+355', flag: '🇦🇱', minLength: 8, maxLength: 9 },
  { code: 'DZ', name: 'Algeria', dialCode: '+213', flag: '🇩🇿', minLength: 8, maxLength: 9 },
  { code: 'AS', name: 'American Samoa', dialCode: '+1684', flag: '🇦🇸', minLength: 10, maxLength: 10 },
  { code: 'AD', name: 'Andorra', dialCode: '+376', flag: '🇦🇩', minLength: 6, maxLength: 9 },
  { code: 'AO', name: 'Angola', dialCode: '+244', flag: '🇦🇴', minLength: 9, maxLength: 9 },
  { code: 'AI', name: '<PERSON><PERSON><PERSON>', dialCode: '+1264', flag: '🇦🇮', minLength: 10, maxLength: 10 },
  { code: 'AG', name: 'Antigua and Barbuda', dialCode: '+1268', flag: '🇦🇬', minLength: 10, maxLength: 10 },
  { code: 'AR', name: 'Argentina', dialCode: '+54', flag: '🇦🇷', minLength: 10, maxLength: 11 },
  { code: 'AM', name: 'Armenia', dialCode: '+374', flag: '🇦🇲', minLength: 8, maxLength: 8 },
  { code: 'AW', name: 'Aruba', dialCode: '+297', flag: '🇦🇼', minLength: 7, maxLength: 7 },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺', minLength: 9, maxLength: 9 },
  { code: 'AT', name: 'Austria', dialCode: '+43', flag: '🇦🇹', minLength: 10, maxLength: 12 },
  { code: 'AZ', name: 'Azerbaijan', dialCode: '+994', flag: '🇦🇿', minLength: 8, maxLength: 9 },
  { code: 'BS', name: 'Bahamas', dialCode: '+1242', flag: '🇧🇸', minLength: 10, maxLength: 10 },
  { code: 'BH', name: 'Bahrain', dialCode: '+973', flag: '🇧🇭', minLength: 8, maxLength: 8 },
  { code: 'BD', name: 'Bangladesh', dialCode: '+880', flag: '🇧🇩', minLength: 10, maxLength: 10 },
  { code: 'BB', name: 'Barbados', dialCode: '+1246', flag: '🇧🇧', minLength: 10, maxLength: 10 },
  { code: 'BY', name: 'Belarus', dialCode: '+375', flag: '🇧🇾', minLength: 9, maxLength: 9 },
  { code: 'BE', name: 'Belgium', dialCode: '+32', flag: '🇧🇪', minLength: 8, maxLength: 9 },
  { code: 'BZ', name: 'Belize', dialCode: '+501', flag: '🇧🇿', minLength: 7, maxLength: 7 },
  { code: 'BJ', name: 'Benin', dialCode: '+229', flag: '🇧🇯', minLength: 8, maxLength: 8 },
  { code: 'BM', name: 'Bermuda', dialCode: '+1441', flag: '🇧🇲', minLength: 10, maxLength: 10 },
  { code: 'BT', name: 'Bhutan', dialCode: '+975', flag: '🇧🇹', minLength: 8, maxLength: 8 },
  { code: 'BO', name: 'Bolivia', dialCode: '+591', flag: '🇧🇴', minLength: 8, maxLength: 8 },
  { code: 'BA', name: 'Bosnia and Herzegovina', dialCode: '+387', flag: '🇧🇦', minLength: 8, maxLength: 9 },
  { code: 'BW', name: 'Botswana', dialCode: '+267', flag: '🇧🇼', minLength: 7, maxLength: 8 },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷', minLength: 10, maxLength: 11 },
  { code: 'BN', name: 'Brunei', dialCode: '+673', flag: '🇧🇳', minLength: 7, maxLength: 7 },
  { code: 'BG', name: 'Bulgaria', dialCode: '+359', flag: '🇧🇬', minLength: 8, maxLength: 9 },
  { code: 'BF', name: 'Burkina Faso', dialCode: '+226', flag: '🇧🇫', minLength: 8, maxLength: 8 },
  { code: 'BI', name: 'Burundi', dialCode: '+257', flag: '🇧🇮', minLength: 8, maxLength: 8 },
  { code: 'KH', name: 'Cambodia', dialCode: '+855', flag: '🇰🇭', minLength: 8, maxLength: 9 },
  { code: 'CM', name: 'Cameroon', dialCode: '+237', flag: '🇨🇲', minLength: 8, maxLength: 9 },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦', minLength: 10, maxLength: 10 },
  { code: 'CV', name: 'Cape Verde', dialCode: '+238', flag: '🇨🇻', minLength: 7, maxLength: 7 },
  { code: 'KY', name: 'Cayman Islands', dialCode: '+1345', flag: '🇰🇾', minLength: 10, maxLength: 10 },
  { code: 'CF', name: 'Central African Republic', dialCode: '+236', flag: '🇨🇫', minLength: 8, maxLength: 8 },
  { code: 'TD', name: 'Chad', dialCode: '+235', flag: '🇹🇩', minLength: 8, maxLength: 8 },
  { code: 'CL', name: 'Chile', dialCode: '+56', flag: '🇨🇱', minLength: 8, maxLength: 9 },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳', minLength: 11, maxLength: 11 },
  { code: 'CO', name: 'Colombia', dialCode: '+57', flag: '🇨🇴', minLength: 10, maxLength: 10 },
  { code: 'KM', name: 'Comoros', dialCode: '+269', flag: '🇰🇲', minLength: 7, maxLength: 7 },
  { code: 'CG', name: 'Congo', dialCode: '+242', flag: '🇨🇬', minLength: 9, maxLength: 9 },
  { code: 'CD', name: 'Congo (DRC)', dialCode: '+243', flag: '🇨🇩', minLength: 9, maxLength: 9 },
  { code: 'CK', name: 'Cook Islands', dialCode: '+682', flag: '🇨🇰', minLength: 5, maxLength: 5 },
  { code: 'CR', name: 'Costa Rica', dialCode: '+506', flag: '🇨🇷', minLength: 8, maxLength: 8 },
  { code: 'CI', name: 'Côte d\'Ivoire', dialCode: '+225', flag: '🇨🇮', minLength: 8, maxLength: 10 },
  { code: 'HR', name: 'Croatia', dialCode: '+385', flag: '🇭🇷', minLength: 8, maxLength: 9 },
  { code: 'CU', name: 'Cuba', dialCode: '+53', flag: '🇨🇺', minLength: 8, maxLength: 8 },
  { code: 'CY', name: 'Cyprus', dialCode: '+357', flag: '🇨🇾', minLength: 8, maxLength: 8 },
  { code: 'CZ', name: 'Czech Republic', dialCode: '+420', flag: '🇨🇿', minLength: 9, maxLength: 9 },
  { code: 'DK', name: 'Denmark', dialCode: '+45', flag: '🇩🇰', minLength: 8, maxLength: 8 },
  { code: 'DJ', name: 'Djibouti', dialCode: '+253', flag: '🇩🇯', minLength: 8, maxLength: 8 },
  { code: 'DM', name: 'Dominica', dialCode: '+1767', flag: '🇩🇲', minLength: 10, maxLength: 10 },
  { code: 'DO', name: 'Dominican Republic', dialCode: '+1809', flag: '🇩🇴', minLength: 10, maxLength: 10 },
  { code: 'EC', name: 'Ecuador', dialCode: '+593', flag: '🇪🇨', minLength: 8, maxLength: 9 },
  { code: 'EG', name: 'Egypt', dialCode: '+20', flag: '🇪🇬', minLength: 10, maxLength: 11 },
  { code: 'SV', name: 'El Salvador', dialCode: '+503', flag: '🇸🇻', minLength: 8, maxLength: 8 },
  { code: 'GQ', name: 'Equatorial Guinea', dialCode: '+240', flag: '🇬🇶', minLength: 9, maxLength: 9 },
  { code: 'ER', name: 'Eritrea', dialCode: '+291', flag: '🇪🇷', minLength: 7, maxLength: 7 },
  { code: 'EE', name: 'Estonia', dialCode: '+372', flag: '🇪🇪', minLength: 7, maxLength: 8 },
  { code: 'ET', name: 'Ethiopia', dialCode: '+251', flag: '🇪🇹', minLength: 9, maxLength: 9 },
  { code: 'FJ', name: 'Fiji', dialCode: '+679', flag: '🇫🇯', minLength: 7, maxLength: 7 },
  { code: 'FI', name: 'Finland', dialCode: '+358', flag: '🇫🇮', minLength: 9, maxLength: 10 },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷', minLength: 9, maxLength: 9 },
  { code: 'GA', name: 'Gabon', dialCode: '+241', flag: '🇬🇦', minLength: 7, maxLength: 8 },
  { code: 'GM', name: 'Gambia', dialCode: '+220', flag: '🇬🇲', minLength: 7, maxLength: 7 },
  { code: 'GE', name: 'Georgia', dialCode: '+995', flag: '🇬🇪', minLength: 9, maxLength: 9 },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪', minLength: 10, maxLength: 12 },
  { code: 'GH', name: 'Ghana', dialCode: '+233', flag: '🇬🇭', minLength: 9, maxLength: 9 },
  { code: 'GR', name: 'Greece', dialCode: '+30', flag: '🇬🇷', minLength: 10, maxLength: 10 },
  { code: 'GD', name: 'Grenada', dialCode: '+1473', flag: '🇬🇩', minLength: 10, maxLength: 10 },
  { code: 'GT', name: 'Guatemala', dialCode: '+502', flag: '🇬🇹', minLength: 8, maxLength: 8 },
  { code: 'GN', name: 'Guinea', dialCode: '+224', flag: '🇬🇳', minLength: 8, maxLength: 9 },
  { code: 'GW', name: 'Guinea-Bissau', dialCode: '+245', flag: '🇬🇼', minLength: 7, maxLength: 7 },
  { code: 'GY', name: 'Guyana', dialCode: '+592', flag: '🇬🇾', minLength: 7, maxLength: 7 },
  { code: 'HT', name: 'Haiti', dialCode: '+509', flag: '🇭🇹', minLength: 8, maxLength: 8 },
  { code: 'HN', name: 'Honduras', dialCode: '+504', flag: '🇭🇳', minLength: 8, maxLength: 8 },
  { code: 'HK', name: 'Hong Kong', dialCode: '+852', flag: '🇭🇰', minLength: 8, maxLength: 8 },
  { code: 'HU', name: 'Hungary', dialCode: '+36', flag: '🇭🇺', minLength: 8, maxLength: 9 },
  { code: 'IS', name: 'Iceland', dialCode: '+354', flag: '🇮🇸', minLength: 7, maxLength: 7 },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳', minLength: 10, maxLength: 10 },
  { code: 'ID', name: 'Indonesia', dialCode: '+62', flag: '🇮🇩', minLength: 9, maxLength: 12 },
  { code: 'IR', name: 'Iran', dialCode: '+98', flag: '🇮🇷', minLength: 10, maxLength: 10 },
  { code: 'IQ', name: 'Iraq', dialCode: '+964', flag: '🇮🇶', minLength: 10, maxLength: 10 },
  { code: 'IE', name: 'Ireland', dialCode: '+353', flag: '🇮🇪', minLength: 9, maxLength: 9 },
  { code: 'IL', name: 'Israel', dialCode: '+972', flag: '🇮🇱', minLength: 8, maxLength: 9 },
  { code: 'IT', name: 'Italy', dialCode: '+39', flag: '🇮🇹', minLength: 9, maxLength: 11 },
  { code: 'JM', name: 'Jamaica', dialCode: '+1876', flag: '🇯🇲', minLength: 10, maxLength: 10 },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵', minLength: 10, maxLength: 11 },
  { code: 'JO', name: 'Jordan', dialCode: '+962', flag: '🇯🇴', minLength: 8, maxLength: 9 },
  { code: 'KZ', name: 'Kazakhstan', dialCode: '+7', flag: '🇰🇿', minLength: 10, maxLength: 10 },
  { code: 'KE', name: 'Kenya', dialCode: '+254', flag: '🇰🇪', minLength: 9, maxLength: 9 },
  { code: 'KW', name: 'Kuwait', dialCode: '+965', flag: '🇰🇼', minLength: 8, maxLength: 8 },
  { code: 'KG', name: 'Kyrgyzstan', dialCode: '+996', flag: '🇰🇬', minLength: 9, maxLength: 9 },
  { code: 'LA', name: 'Laos', dialCode: '+856', flag: '🇱🇦', minLength: 8, maxLength: 10 },
  { code: 'LV', name: 'Latvia', dialCode: '+371', flag: '🇱🇻', minLength: 8, maxLength: 8 },
  { code: 'LB', name: 'Lebanon', dialCode: '+961', flag: '🇱🇧', minLength: 7, maxLength: 8 },
  { code: 'LS', name: 'Lesotho', dialCode: '+266', flag: '🇱🇸', minLength: 8, maxLength: 8 },
  { code: 'LR', name: 'Liberia', dialCode: '+231', flag: '🇱🇷', minLength: 7, maxLength: 8 },
  { code: 'LY', name: 'Libya', dialCode: '+218', flag: '🇱🇾', minLength: 9, maxLength: 9 },
  { code: 'LI', name: 'Liechtenstein', dialCode: '+423', flag: '🇱🇮', minLength: 7, maxLength: 7 },
  { code: 'LT', name: 'Lithuania', dialCode: '+370', flag: '🇱🇹', minLength: 8, maxLength: 8 },
  { code: 'LU', name: 'Luxembourg', dialCode: '+352', flag: '🇱🇺', minLength: 9, maxLength: 9 },
  { code: 'MO', name: 'Macau', dialCode: '+853', flag: '🇲🇴', minLength: 8, maxLength: 8 },
  { code: 'MK', name: 'Macedonia', dialCode: '+389', flag: '🇲🇰', minLength: 8, maxLength: 8 },
  { code: 'MG', name: 'Madagascar', dialCode: '+261', flag: '🇲🇬', minLength: 9, maxLength: 9 },
  { code: 'MW', name: 'Malawi', dialCode: '+265', flag: '🇲🇼', minLength: 8, maxLength: 9 },
  { code: 'MY', name: 'Malaysia', dialCode: '+60', flag: '🇲🇾', minLength: 9, maxLength: 10 },
  { code: 'MV', name: 'Maldives', dialCode: '+960', flag: '🇲🇻', minLength: 7, maxLength: 7 },
  { code: 'ML', name: 'Mali', dialCode: '+223', flag: '🇲🇱', minLength: 8, maxLength: 8 },
  { code: 'MT', name: 'Malta', dialCode: '+356', flag: '🇲🇹', minLength: 8, maxLength: 8 },
  { code: 'MR', name: 'Mauritania', dialCode: '+222', flag: '🇲🇷', minLength: 8, maxLength: 8 },
  { code: 'MU', name: 'Mauritius', dialCode: '+230', flag: '🇲🇺', minLength: 7, maxLength: 8 },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽', minLength: 10, maxLength: 10 },
  { code: 'MD', name: 'Moldova', dialCode: '+373', flag: '🇲🇩', minLength: 8, maxLength: 8 },
  { code: 'MC', name: 'Monaco', dialCode: '+377', flag: '🇲🇨', minLength: 8, maxLength: 9 },
  { code: 'MN', name: 'Mongolia', dialCode: '+976', flag: '🇲🇳', minLength: 8, maxLength: 8 },
  { code: 'ME', name: 'Montenegro', dialCode: '+382', flag: '🇲🇪', minLength: 8, maxLength: 9 },
  { code: 'MA', name: 'Morocco', dialCode: '+212', flag: '🇲🇦', minLength: 9, maxLength: 9 },
  { code: 'MZ', name: 'Mozambique', dialCode: '+258', flag: '🇲🇿', minLength: 8, maxLength: 9 },
  { code: 'MM', name: 'Myanmar', dialCode: '+95', flag: '🇲🇲', minLength: 8, maxLength: 10 },
  { code: 'NA', name: 'Namibia', dialCode: '+264', flag: '🇳🇦', minLength: 7, maxLength: 8 },
  { code: 'NP', name: 'Nepal', dialCode: '+977', flag: '🇳🇵', minLength: 10, maxLength: 10 },
  { code: 'NL', name: 'Netherlands', dialCode: '+31', flag: '🇳🇱', minLength: 9, maxLength: 9 },
  { code: 'NZ', name: 'New Zealand', dialCode: '+64', flag: '🇳🇿', minLength: 8, maxLength: 10 },
  { code: 'NI', name: 'Nicaragua', dialCode: '+505', flag: '🇳🇮', minLength: 8, maxLength: 8 },
  { code: 'NE', name: 'Niger', dialCode: '+227', flag: '🇳🇪', minLength: 8, maxLength: 8 },
  { code: 'NG', name: 'Nigeria', dialCode: '+234', flag: '🇳🇬', minLength: 10, maxLength: 10 },
  { code: 'NO', name: 'Norway', dialCode: '+47', flag: '🇳🇴', minLength: 8, maxLength: 8 },
  { code: 'OM', name: 'Oman', dialCode: '+968', flag: '🇴🇲', minLength: 8, maxLength: 8 },
  { code: 'PK', name: 'Pakistan', dialCode: '+92', flag: '🇵🇰', minLength: 10, maxLength: 10 },
  { code: 'PS', name: 'Palestine', dialCode: '+970', flag: '🇵🇸', minLength: 9, maxLength: 9 },
  { code: 'PA', name: 'Panama', dialCode: '+507', flag: '🇵🇦', minLength: 7, maxLength: 8 },
  { code: 'PY', name: 'Paraguay', dialCode: '+595', flag: '🇵🇾', minLength: 8, maxLength: 9 },
  { code: 'PE', name: 'Peru', dialCode: '+51', flag: '🇵🇪', minLength: 8, maxLength: 9 },
  { code: 'PH', name: 'Philippines', dialCode: '+63', flag: '🇵🇭', minLength: 10, maxLength: 10 },
  { code: 'PL', name: 'Poland', dialCode: '+48', flag: '🇵🇱', minLength: 9, maxLength: 9 },
  { code: 'PT', name: 'Portugal', dialCode: '+351', flag: '🇵🇹', minLength: 9, maxLength: 9 },
  { code: 'QA', name: 'Qatar', dialCode: '+974', flag: '🇶🇦', minLength: 8, maxLength: 8 },
  { code: 'RO', name: 'Romania', dialCode: '+40', flag: '🇷🇴', minLength: 9, maxLength: 9 },
  { code: 'RU', name: 'Russia', dialCode: '+7', flag: '🇷🇺', minLength: 10, maxLength: 10 },
  { code: 'RW', name: 'Rwanda', dialCode: '+250', flag: '🇷🇼', minLength: 9, maxLength: 9 },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966', flag: '🇸🇦', minLength: 9, maxLength: 9 },
  { code: 'SN', name: 'Senegal', dialCode: '+221', flag: '🇸🇳', minLength: 9, maxLength: 9 },
  { code: 'RS', name: 'Serbia', dialCode: '+381', flag: '🇷🇸', minLength: 8, maxLength: 9 },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬', minLength: 8, maxLength: 8 },
  { code: 'SK', name: 'Slovakia', dialCode: '+421', flag: '🇸🇰', minLength: 9, maxLength: 9 },
  { code: 'SI', name: 'Slovenia', dialCode: '+386', flag: '🇸🇮', minLength: 8, maxLength: 8 },
  { code: 'ZA', name: 'South Africa', dialCode: '+27', flag: '🇿🇦', minLength: 9, maxLength: 9 },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷', minLength: 10, maxLength: 11 },
  { code: 'ES', name: 'Spain', dialCode: '+34', flag: '🇪🇸', minLength: 9, maxLength: 9 },
  { code: 'LK', name: 'Sri Lanka', dialCode: '+94', flag: '🇱🇰', minLength: 9, maxLength: 9 },
  { code: 'SD', name: 'Sudan', dialCode: '+249', flag: '🇸🇩', minLength: 9, maxLength: 9 },
  { code: 'SE', name: 'Sweden', dialCode: '+46', flag: '🇸🇪', minLength: 9, maxLength: 9 },
  { code: 'CH', name: 'Switzerland', dialCode: '+41', flag: '🇨🇭', minLength: 9, maxLength: 9 },
  { code: 'TW', name: 'Taiwan', dialCode: '+886', flag: '🇹🇼', minLength: 9, maxLength: 9 },
  { code: 'TH', name: 'Thailand', dialCode: '+66', flag: '🇹🇭', minLength: 9, maxLength: 9 },
  { code: 'TR', name: 'Turkey', dialCode: '+90', flag: '🇹🇷', minLength: 10, maxLength: 10 },
  { code: 'UA', name: 'Ukraine', dialCode: '+380', flag: '🇺🇦', minLength: 9, maxLength: 9 },
  { code: 'AE', name: 'United Arab Emirates', dialCode: '+971', flag: '🇦🇪', minLength: 9, maxLength: 9 },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧', minLength: 10, maxLength: 10 },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸', minLength: 10, maxLength: 10 },
  { code: 'UY', name: 'Uruguay', dialCode: '+598', flag: '🇺🇾', minLength: 8, maxLength: 8 },
  { code: 'VE', name: 'Venezuela', dialCode: '+58', flag: '🇻🇪', minLength: 10, maxLength: 10 },
  { code: 'VN', name: 'Vietnam', dialCode: '+84', flag: '🇻🇳', minLength: 9, maxLength: 10 },
  { code: 'ZM', name: 'Zambia', dialCode: '+260', flag: '🇿🇲', minLength: 9, maxLength: 9 },
  { code: 'ZW', name: 'Zimbabwe', dialCode: '+263', flag: '🇿🇼', minLength: 9, maxLength: 9 },
];

// Helper functions
export const getCountryByCode = (code: string): CountryData | undefined => {
  return countryCodesData.find(country => country.code === code);
};

export const getCountryByDialCode = (dialCode: string): CountryData | undefined => {
  return countryCodesData.find(country => country.dialCode === dialCode);
};

export const searchCountries = (query: string): CountryData[] => {
  const searchTerm = query.toLowerCase();
  return countryCodesData.filter(country => 
    country.name.toLowerCase().includes(searchTerm) ||
    country.dialCode.includes(searchTerm) ||
    country.code.toLowerCase().includes(searchTerm)
  );
};
