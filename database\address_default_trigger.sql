-- Drop existing function if it exists
DROP FUNCTION IF EXISTS public.set_default_address;

-- Create trigger function to handle default address logic
CREATE OR REPLACE FUNCTION public.handle_default_address()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is the first address or explicitly setting as default
    IF (
        (SELECT COUNT(*) FROM public.user_addresses WHERE user_id = NEW.user_id) = 0
        OR NEW.is_default = true
    ) THEN
        -- Set all other addresses for this user to non-default
        UPDATE public.user_addresses
        SET is_default = false
        WHERE user_id = NEW.user_id
        AND id != NEW.id;
        
        -- Ensure this address is default
        NEW.is_default := true;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger
DROP TRIGGER IF EXISTS handle_default_address ON public.user_addresses;
CREATE TRIGGER handle_default_address
    BEFORE INSERT OR UPDATE ON public.user_addresses
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_default_address();
