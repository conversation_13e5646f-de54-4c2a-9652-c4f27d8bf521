@keyframes marquee {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@keyframes mobilemarquee {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

.marquee {
  animation: marquee linear infinite;
  transition: animation-play-state 0.5s ease-out;
  will-change: transform;
}

.mobilemarquee {
  animation: mobilemarquee 45s linear infinite;
  will-change: transform;
}

.paused {
  animation-play-state: paused !important;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Modern fade-in animations for CategorySection */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fadeInUp {
  animation: fadeInUp 1s ease-out forwards;
}

.fadeInLeft {
  animation: fadeInLeft 1s ease-out forwards;
}

.fadeInRight {
  animation: fadeInRight 1s ease-out forwards;
}

/* Advanced animations for DiscountedProducts section */
@keyframes bounceInScale {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px) rotate(10deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10px) rotate(-2deg);
  }
  70% {
    transform: scale(0.95) translateY(5px) rotate(1deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotate(0deg);
  }
}

@keyframes floatTitle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(0.5deg);
  }
}

@keyframes shimmerWave {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
  }
}

@keyframes magneticHover {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.02) rotate(-0.5deg);
  }
  100% {
    transform: scale(1.05) rotate(-1deg);
  }
}

.bounceInScale {
  animation: bounceInScale 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.floatTitle {
  animation: floatTitle 3s ease-in-out infinite;
}

.shimmerWave {
  animation: shimmerWave 2s ease-in-out infinite;
}

.magneticHover:hover {
  animation: magneticHover 0.3s ease-out forwards;
}

/* Advanced animations for WhyChooseSection */
@keyframes magneticMorph {
  0% {
    opacity: 0;
    transform: scale(0) rotate(180deg) translateY(50px);
    filter: blur(10px);
  }
  60% {
    opacity: 0.8;
    transform: scale(1.1) rotate(-10deg) translateY(-10px);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg) translateY(0);
    filter: blur(0px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
  }
}

@keyframes pulseRing {
  0% {
    opacity: 1;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

.magneticMorph {
  animation: magneticMorph 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

.pulseRing {
  animation: pulseRing 2s ease-out infinite;
}

.textReveal {
  animation: textReveal 0.6s ease-out forwards;
}
