-- Update existing products with discount information
-- Run this in Supabase SQL Editor to add discount data to existing products

UPDATE public.products SET 
  original_price = 34.99,
  discount = '30% OFF'
WHERE id = 'sp1';

UPDATE public.products SET 
  original_price = 25.99,
  discount = '25% OFF'
WHERE id = 'sp2';

UPDATE public.products SET 
  original_price = 18.99,
  discount = '35% OFF'
WHERE id = 'sp4';

UPDATE public.products SET 
  original_price = 29.99,
  discount = '30% OFF'
WHERE id = 'sp5';

UPDATE public.products SET 
  original_price = 32.99,
  discount = '35% OFF'
WHERE id = 'sp7';

UPDATE public.products SET 
  original_price = 27.99,
  discount = '30% OFF'
WHERE id = 'sp9';

UPDATE public.products SET 
  original_price = 24.99,
  discount = '30% OFF'
WHERE id = 'sp10';
