@keyframes marquee {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.marquee {
  animation: marquee linear infinite;
}

.marquee.paused {
  animation-play-state: paused;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes cartTextFadeOut {
  0% { 
    opacity: 1;
    transform: translateY(0);
  }
  100% { 
    opacity: 0;
    transform: translateY(-4px);
  }
}

@keyframes cartIconSlideRight {
  0% { 
    transform: translateX(0);
    opacity: 1;
  }
  70% {
    transform: translateX(300%);
    opacity: 1;
  }
  100% { 
    transform: translateX(400%);
    opacity: 0;
  }
}

@keyframes loadingCircleSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes checkmarkAppear {
  0% { 
    opacity: 0;
    transform: translateX(-50%) scale(0.5);
  }
  50% { 
    opacity: 1;
    transform: translateX(-50%) scale(1.2);
  }
  100% { 
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

.cartButton {
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.cartText {
  transition: opacity 0.3s ease;
  position: relative;
}

.cartIcon {
  position: relative;
  margin-right: 0.5rem;
  transition: opacity 0.3s ease;
  opacity: 1;
}

.fadeIn {
  opacity: 0;
  animation: fadeInBoth 0.3s ease forwards;
}

@keyframes fadeInBoth {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.cartTextFading {
  animation: cartTextFadeOut 0.3s ease forwards;
}

.cartIconSliding {
  animation: cartIconSlideRight 0.4s ease forwards;
}

.loadingSpinner {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 24px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: loadingCircleSpin 0.8s linear infinite;
}

.checkmark {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) scale(0.5);
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkmarkAppearing {
  animation: checkmarkAppear 0.5s ease forwards;
}

.successBackground {
  background: linear-gradient(to right, #22c55e, #16a34a) !important;
  transition: all 0.5s ease;
  transform: translateZ(0);
}
