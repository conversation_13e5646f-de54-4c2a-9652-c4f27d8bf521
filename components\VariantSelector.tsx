import React from 'react';
import { ProductVariant } from '../types';

interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariantId: string;
  onVariantSelect: (variantId: string) => void;
  className?: string;
}

const VariantSelector: React.FC<VariantSelectorProps> = ({
  variants,
  selectedVariantId,
  onVariantSelect,
  className = ''
}) => {
  if (!variants || variants.length <= 1) {
    return null;
  }

  return (
    <div className={`${className}`}>
      <h4 className="text-sm font-medium text-gray-700 mb-3">Available Flavors:</h4>
      <div className="flex flex-wrap gap-3">
        {variants.map((variant) => {
          const isSelected = variant.id === selectedVariantId;
          
          return (
            <button
              key={variant.id}
              onClick={() => onVariantSelect(variant.id)}
              className={`group relative flex items-center space-x-3 px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
                isSelected
                  ? 'border-brand-accent-teal bg-brand-accent-teal/10 shadow-lg'
                  : 'border-gray-200 bg-white/80 hover:border-brand-accent-teal/50 hover:bg-white/95'
              }`}
            >
              {/* Variant Image */}
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 flex-shrink-0">
                <img
                  src={variant.imageUrl}
                  alt={variant.name}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              
              {/* Variant Name */}
              <div className="text-left">
                <p className={`text-sm font-medium transition-colors duration-300 ${
                  isSelected ? 'text-brand-accent-teal' : 'text-gray-700 group-hover:text-brand-accent-teal'
                }`}>
                  {variant.name}
                </p>
                
                {/* Color indicator if available */}
                {variant.color && (
                  <div className="flex items-center space-x-2 mt-1">
                    <div
                      className="w-3 h-3 rounded-full border border-white shadow-sm"
                      style={{ backgroundColor: variant.color.hex }}
                    />
                    <span className="text-xs text-gray-500">{variant.color.name}</span>
                  </div>
                )}
              </div>
              
              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-brand-accent-teal rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default VariantSelector;
