export interface ProductColor {
  name: string;
  hex: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  imageUrl: string;
  color?: ProductColor;
}

export interface Product {
  id: string;
  name: string;
  category?: string;
  price: number;
  originalPrice?: number;
  original_price?: number; // Database field name
  discount?: string;
  imageUrl?: string; // Frontend field name
  image_url?: string; // Database field name
  description?: string;
  material?: string;
  colors?: ProductColor[];
  availableColorCount?: number;
  features?: string[];
  variants?: ProductVariant[];
  selectedVariant?: string; // ID of currently selected variant
}

export interface CategoryInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

// Cart-related types
export interface CartItem {
  id: string;
  user_id: string;
  product_id: string;
  variant_id?: string; // Added for product variants
  quantity: number;
  created_at: string;
  updated_at: string;
  product?: Product; // Populated when fetching cart with product details
}

export interface CartSummary {
  subtotal: number;
  tax: number;
  total: number;
  itemCount: number;
  totalSavings?: number;
  originalTotal?: number;
}

// Address-related types
export type AddressType = 'home' | 'work' | 'other';

export interface Address {
  id: string;
  user_id: string;
  address_type: AddressType;
  full_name: string;
  phone_number: string;
  street_address: string;
  apartment?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface AddressFormData {
  address_type: AddressType;
  full_name: string;
  phone_number: string;
  street_address: string;
  apartment?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface AddressContextType {
  addresses: Address[];
  loading: boolean;
  error: string | null;
  selectedAddress: Address | null;
  defaultAddress: Address | null;
  addAddress: (data: AddressFormData) => Promise<Address | null>;
  updateAddress: (id: string, data: AddressFormData) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
  selectAddress: (address: Address | null) => void;
  refreshAddresses: () => Promise<void>;
}
