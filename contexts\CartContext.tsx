import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import { CartItem, CartSummary, Product } from '../types';

interface CartContextType {
  cartItems: CartItem[];
  cartSummary: CartSummary;
  loading: boolean;
  addToCart: (productId: string, quantity?: number, variantId?: string) => Promise<{ success: boolean; error?: string }>;
  removeFromCart: (cartItemId: string) => Promise<{ success: boolean; error?: string }>;
  updateQuantity: (cartItemId: string, quantity: number) => Promise<{ success: boolean; error?: string }>;
  clearCart: () => Promise<{ success: boolean; error?: string }>;
  refreshCart: () => Promise<void>;
  isInCart: (productId: string) => boolean;
  getCartItemQuantity: (productId: string) => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: React.ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Calculate cart summary
  const cartSummary: CartSummary = React.useMemo(() => {
    const subtotal = cartItems.reduce((sum, item) => {
      const price = item.product?.price || 0;
      return sum + (price * item.quantity);
    }, 0);

    // Calculate total savings
    const totalSavings = cartItems.reduce((sum, item) => {
      const originalPrice = item.product?.original_price || item.product?.price || 0;
      const currentPrice = item.product?.price || 0;
      const savings = originalPrice - currentPrice;
      return sum + (savings * item.quantity);
    }, 0);

    // Calculate what the original total would have been
    const originalTotal = cartItems.reduce((sum, item) => {
      const originalPrice = item.product?.original_price || item.product?.price || 0;
      return sum + (originalPrice * item.quantity);
    }, 0);

    const total = subtotal; // No tax calculation
    const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

    return {
      subtotal: Number(subtotal.toFixed(2)),
      tax: 0, // No tax
      total: Number(total.toFixed(2)),
      itemCount,
      totalSavings: Number(totalSavings.toFixed(2)),
      originalTotal: Number(originalTotal.toFixed(2))
    };
  }, [cartItems]);

  // Fetch cart items from database
  const fetchCartItems = async () => {
    if (!user) {
      setCartItems([]);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(
            id,
            name,
            category,
            price,
            original_price,
            discount,
            image_url,
            description,
            material,
            features,
            variants
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching cart items:', error);
        return;
      }

      setCartItems(data || []);
    } catch (error) {
      console.error('Unexpected error fetching cart:', error);
    } finally {
      setLoading(false);
    }
  };

  // Add item to cart
  const addToCart = async (productId: string, quantity: number = 1, variantId?: string) => {
    if (!user) {
      return { success: false, error: 'Please log in to add items to cart' };
    }

    try {
      // Find existing item in local state first
      const existingItem = cartItems.find(item =>
        item.product_id === productId && item.variant_id === (variantId || null)
      );

      // Optimistically update local state
      if (existingItem) {
        const newQuantity = existingItem.quantity + quantity;
        setCartItems(prevItems =>
          prevItems.map(item =>
            item.id === existingItem.id
              ? { ...item, quantity: newQuantity }
              : item
          )
        );

        // Update in database
        const { error: updateError } = await supabase
          .from('cart_items')
          .update({ quantity: newQuantity })
          .eq('id', existingItem.id);

        if (updateError) {
          // Revert optimistic update on error
          setCartItems(prevItems =>
            prevItems.map(item =>
              item.id === existingItem.id
                ? { ...item, quantity: existingItem.quantity }
                : item
            )
          );
          console.error('Error updating cart item:', updateError);
          return { success: false, error: 'Failed to update cart item' };
        }
      } else {
        // For new items, first insert into database
        const { data: newItem, error: insertError } = await supabase
          .from('cart_items')
          .insert({
            user_id: user.id,
            product_id: productId,
            variant_id: variantId || null,
            quantity
          })
          .select('*, product:products(*)')
          .single();

        if (insertError || !newItem) {
          console.error('Error adding to cart:', insertError);
          return { success: false, error: 'Failed to add item to cart' };
        }

        // Update local state with the new item
        setCartItems(prevItems => [newItem, ...prevItems]);
      }

      return { success: true };
    } catch (error) {
      console.error('Unexpected error adding to cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Remove item from cart
  const removeFromCart = async (cartItemId: string) => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', cartItemId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error removing from cart:', error);
        return { success: false, error: 'Failed to remove item from cart' };
      }

      await fetchCartItems();
      return { success: true };
    } catch (error) {
      console.error('Unexpected error removing from cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Update item quantity with corrected state management
  const updateQuantity = async (cartItemId: string, quantity: number) => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    if (quantity <= 0) {
      return removeFromCart(cartItemId);
    }

    try {
      // Find the target item and its duplicates
      const targetItem = cartItems.find(item => item.id === cartItemId);
      if (!targetItem) {
        return { success: false, error: 'Item not found' };
      }

      // Perform the database update first
      const { error } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', cartItemId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating quantity:', error);
        return { success: false, error: 'Failed to update quantity' };
      }

      // Update local state after successful database update
      setCartItems(prevItems => {
        const updatedItems = prevItems.map(item => {
          if (item.id === cartItemId) {
            return { ...item, quantity };
          }
          return item;
        });
        return updatedItems;
      });

      return { success: true };
    } catch (error) {
      console.error('Unexpected error updating quantity:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Clear entire cart
  const clearCart = async () => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error clearing cart:', error);
        return { success: false, error: 'Failed to clear cart' };
      }

      setCartItems([]);
      return { success: true };
    } catch (error) {
      console.error('Unexpected error clearing cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Refresh cart data
  const refreshCart = async () => {
    await fetchCartItems();
  };

  // Check if product is in cart
  const isInCart = (productId: string): boolean => {
    return cartItems.some(item => item.product_id === productId);
  };

  // Get quantity of product in cart
  const getCartItemQuantity = (productId: string): number => {
    const item = cartItems.find(item => item.product_id === productId);
    return item ? item.quantity : 0;
  };

  // Fetch cart items when user changes
  useEffect(() => {
    fetchCartItems();
  }, [user]);

  const value = {
    cartItems,
    cartSummary,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart,
    isInCart,
    getCartItemQuantity,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
