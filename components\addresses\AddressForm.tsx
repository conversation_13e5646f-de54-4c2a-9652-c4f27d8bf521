import React, { useState } from 'react';
import { AddressFormData, AddressType } from '../../types';

interface AddressFormProps {
  initialData?: Partial<AddressFormData>;
  onSubmit: (data: AddressFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

const AddressForm: React.FC<AddressFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  const [formData, setFormData] = useState<AddressFormData>({
    address_type: initialData?.address_type || 'home',
    full_name: initialData?.full_name || '',
    phone_number: initialData?.phone_number || '',
    street_address: initialData?.street_address || '',
    apartment: initialData?.apartment || '',
    city: initialData?.city || '',
    state: initialData?.state || '',
    postal_code: initialData?.postal_code || '',
    country: initialData?.country || ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof AddressFormData, string>>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof AddressFormData, string>> = {};
    
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    }
    
    if (!formData.phone_number.trim()) {
      newErrors.phone_number = 'Phone number is required';
    } else if (!/^\+?[\d\s-]+$/.test(formData.phone_number)) {
      newErrors.phone_number = 'Invalid phone number format';
    }
    
    if (!formData.street_address.trim()) {
      newErrors.street_address = 'Street address is required';
    }
    
    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }
    
    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }
    
    if (!formData.postal_code.trim()) {
      newErrors.postal_code = 'Postal code is required';
    }
    
    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);
  };

  const handleChange = (
    field: keyof AddressFormData,
    value: string
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Address Type Selector */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Address Type
        </label>
        <div className="flex space-x-4">
          {(['home', 'work', 'other'] as AddressType[]).map((type) => (
            <button
              key={type}
              type="button"
              onClick={() => handleChange('address_type', type)}
              className={`px-4 py-2 rounded-lg border-2 transition-all duration-200 ${
                formData.address_type === type
                  ? 'border-brand-accent-teal bg-brand-accent-teal text-white'
                  : 'border-gray-200 hover:border-brand-accent-teal/50'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Full Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Full Name*
        </label>
        <input
          type="text"
          value={formData.full_name}
          onChange={(e) => handleChange('full_name', e.target.value)}
          className={`w-full px-4 py-2 rounded-lg border ${
            errors.full_name
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:ring-brand-accent-teal'
          } focus:outline-none focus:ring-2 transition-colors`}
          placeholder="Enter your full name"
        />
        {errors.full_name && (
          <p className="mt-1 text-sm text-red-500">{errors.full_name}</p>
        )}
      </div>

      {/* Phone Number */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Phone Number*
          <span className="block text-sm font-normal text-gray-500">
            Please include your country code (e.g. +1, +44, +91)
          </span>
        </label>
        <input
          type="tel"
          value={formData.phone_number}
          onChange={(e) => handleChange('phone_number', e.target.value)}
          className={`w-full px-4 py-2 rounded-lg border ${
            errors.phone_number
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:ring-brand-accent-teal'
          } focus:outline-none focus:ring-2 transition-colors`}
          placeholder="Enter your phone number with country code"
        />
        {errors.phone_number && (
          <p className="mt-1 text-sm text-red-500">{errors.phone_number}</p>
        )}
      </div>

      {/* Street Address */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Street Address*
        </label>
        <input
          type="text"
          value={formData.street_address}
          onChange={(e) => handleChange('street_address', e.target.value)}
          className={`w-full px-4 py-2 rounded-lg border ${
            errors.street_address
              ? 'border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:ring-brand-accent-teal'
          } focus:outline-none focus:ring-2 transition-colors`}
          placeholder="Enter your street address"
        />
        {errors.street_address && (
          <p className="mt-1 text-sm text-red-500">{errors.street_address}</p>
        )}
      </div>

      {/* Apartment/Suite */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Apartment/Suite (Optional)
        </label>
        <input
          type="text"
          value={formData.apartment || ''}
          onChange={(e) => handleChange('apartment', e.target.value)}
          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-brand-accent-teal focus:outline-none focus:ring-2 transition-colors"
          placeholder="Apartment, suite, unit, etc."
        />
      </div>

      {/* City and State */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            City*
          </label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleChange('city', e.target.value)}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.city
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-brand-accent-teal'
            } focus:outline-none focus:ring-2 transition-colors`}
            placeholder="Enter city"
          />
          {errors.city && (
            <p className="mt-1 text-sm text-red-500">{errors.city}</p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            State*
          </label>
          <input
            type="text"
            value={formData.state}
            onChange={(e) => handleChange('state', e.target.value)}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.state
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-brand-accent-teal'
            } focus:outline-none focus:ring-2 transition-colors`}
            placeholder="Enter state"
          />
          {errors.state && (
            <p className="mt-1 text-sm text-red-500">{errors.state}</p>
          )}
        </div>
      </div>

      {/* Postal Code and Country */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Postal Code*
          </label>
          <input
            type="text"
            value={formData.postal_code}
            onChange={(e) => handleChange('postal_code', e.target.value)}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.postal_code
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-brand-accent-teal'
            } focus:outline-none focus:ring-2 transition-colors`}
            placeholder="Enter postal code"
          />
          {errors.postal_code && (
            <p className="mt-1 text-sm text-red-500">{errors.postal_code}</p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Country*
          </label>
          <input
            type="text"
            value={formData.country}
            onChange={(e) => handleChange('country', e.target.value)}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.country
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:ring-brand-accent-teal'
            } focus:outline-none focus:ring-2 transition-colors`}
            placeholder="Enter country"
          />
          {errors.country && (
            <p className="mt-1 text-sm text-red-500">{errors.country}</p>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-2 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-2 bg-brand-accent-teal text-white rounded-lg hover:bg-brand-accent-teal-darker transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isSubmitting ? (
            <>
              <span className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
              Saving...
            </>
          ) : (
            'Save Address'
          )}
        </button>
      </div>
    </form>
  );
};

export default AddressForm;
