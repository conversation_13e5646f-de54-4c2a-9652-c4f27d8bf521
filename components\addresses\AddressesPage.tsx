import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import AddressList from './AddressList';

const AddressesPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Redirect if not logged in
  React.useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Manage Addresses
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Add and manage your delivery addresses
          </p>
        </div>

        {/* Addresses Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 p-8">
            <AddressList />
          </div>
        </div>

        {/* Back to Profile Button */}
        <div className="mt-8 text-center">
          <button
            onClick={() => navigate('/profile')}
            className="inline-flex items-center text-brand-accent-teal hover:text-brand-accent-teal-darker transition-colors"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to Profile
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddressesPage;
